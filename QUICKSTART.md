# CSV Integration Sheet - Quick Start Guide

This guide will help you get started with the CSV Integration Sheet application for integrating Google Sheets with TOTVS CRM.

## Prerequisites

1. A Google account with access to Google Sheets
2. Access credentials for TOTVS CRM
3. Basic understanding of spreadsheets and data management

## Setup

### 1. Open the Spreadsheet

Open the Google Spreadsheet that has the CSV Integration Sheet script attached.

### 2. Configure the Application

The application requires a configuration sheet named "configuration" with the following key-value pairs:

| Key | Value | Description |
|-----|-------|-------------|
| auth_token | YOUR_AUTH_TOKEN | Authentication token for the CRM API |
| api_base_url | https://totvscrm.app/api/v1/integration/record | Base URL for the CRM API |
| gateway_send_url | https://csvbox-gateway.satellite.totvscrm.app/ | URL for sending data to the gateway |
| upsert_importation_url | YOUR_UPSERT_URL | URL for the upsert importation page |
| delete_importation_url | YOUR_DELETE_URL | URL for the delete importation page |
| importation_identifier | YOUR_IDENTIFIER | Identifier for the importation process |

To set up the configuration:

1. Create a sheet named "configuration" if it doesn't exist
2. Add the keys in column A and the values in column B
3. Replace the placeholder values with your actual credentials and URLs

### 3. Set Up Data Sheets

The application requires the following data sheets:

#### Price List Items Sheet

Create a sheet named "priceListItems" with the following columns:

- `product.code`: Product code
- `priceList.code`: Price list code
- `salesPrice`: Sales price

#### Customers Sheet

Create a sheet named "customers" with the following columns:

- `code`: Customer code
- `name`: Customer name
- `customerPersonType.code`: Customer person type code

## Using the Application

### Accessing the Menu

After opening the spreadsheet, you'll see a custom menu called "Integration Options" in the menu bar. Click on it to see the available options:

- **[Upsert] Price List Items**: Update or insert price list items
- **[Upsert] Customers**: Update or insert customers
- **Generate and Download CSV**: Generate and download a CSV file
- **Open Upsert Import Page**: Open the upsert importation page
- **Open Delete Import Page**: Open the delete importation page

### Updating Price List Items

To update price list items:

1. Enter the product codes in the `product.code` column
2. Enter the price list codes in the `priceList.code` column
3. Enter the sales prices in the `salesPrice` column
4. Click on "Integration Options" > "[Upsert] Price List Items"
5. The application will:
   - Create a new sheet with the current date appended to the name
   - Fetch product and price list data from the CRM
   - Replace codes with IDs
   - Send the data to the CRM for upsert
   - Record the results in the new sheet

### Updating Customers

To update customers:

1. Enter the customer codes in the `code` column
2. Enter the customer names in the `name` column
3. Enter the customer person type codes in the `customerPersonType.code` column
4. Click on "Integration Options" > "[Upsert] Customers"
5. The application will:
   - Create a new sheet with the current date appended to the name
   - Fetch customer person type data from the CRM
   - Replace codes with IDs
   - Send the data to the CRM for upsert
   - Record the results in the new sheet

### Generating and Downloading CSV

To generate and download a CSV file:

1. Click on "Integration Options" > "Generate and Download CSV"
2. The application will generate a CSV file with the data from the current sheet
3. The CSV file will be downloaded to your computer

### Opening Import Pages

To open the upsert or delete importation pages:

1. Click on "Integration Options" > "Open Upsert Import Page" or "Open Delete Import Page"
2. The application will open the corresponding page in a new browser tab

## Troubleshooting

### Common Issues

1. **"Configuration not found" error**: Ensure the configuration sheet exists and has all required keys
2. **"Authentication failed" error**: Check your auth_token in the configuration sheet
3. **"Sheet not found" error**: Ensure the required sheets (priceListItems, customers) exist
4. **"Column not found" error**: Ensure the required columns exist in the sheets
5. **"API request failed" error**: Check your internet connection and API credentials

### Getting Help

If you encounter issues that you can't resolve:

1. Check the error message for specific information
2. Look for error details in the Apps Script execution log:
   - In the spreadsheet, go to Extensions > Apps Script
   - Click on Executions in the left sidebar
   - Look for error details in the most recent execution

## Best Practices

1. **Back up your data**: Always keep a backup of your data before performing operations
2. **Test with small datasets**: Test with a small amount of data before processing large datasets
3. **Check results**: Always verify the results after performing operations
4. **Use descriptive codes**: Use clear and descriptive codes for products, price lists, etc.
5. **Keep configuration secure**: Don't share your auth_token or other sensitive information

## Next Steps

After getting familiar with the basic functionality, you can:

1. Customize the application to fit your specific needs
2. Automate regular updates using time-driven triggers
3. Extend the application to handle additional data types
4. Create custom reports based on the integration results
