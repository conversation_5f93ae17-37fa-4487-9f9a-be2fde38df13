/**
 * Gets products from the API based on product codes in the sheet
 * @param {Sheet} [sheet] - Optional sheet to get product codes from (defaults to "priceListItems" sheet)
 * @return {Object} Object containing items array with product data
 */
function getProducts(sheet) {
    // If sheet is not provided, get it from the active spreadsheet
    if (!sheet) {
        try {
            sheet = validateSheet("priceListItems");
        } catch (e) {
            logError("Failed to get priceListItems sheet", { error: e.message });
            return { items: [] };
        }
    }

    var productCodes = getProductCodes(sheet);

    if (!productCodes) {
        logWarn("No product codes found in sheet to fetch", { sheetName: sheet.getName() });
        return { items: [] };
    }

    try {
        const integrationFilter = `{code->in->[${productCodes}]}{select->[id,code]}{pageSize->1000}`;
        const parsedResponse = apiGet(
            "product",
            "products-integration",
            "v2",
            integrationFilter
        );

        return processApiResponse(
            parsedResponse,
            item => item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined',
            "getProducts"
        );
    } catch (e) {
        logException("Error fetching products", e, { productCodes });
        throw e; // Propagate error with new structure
    }
}

/**
 * Gets product codes from a sheet
 * @param {Sheet} sheet - The sheet to get product codes from
 * @return {string} Comma-separated list of product codes
 */
function getProductCodes(sheet) {
  if (!sheet) {
    logWarn("Sheet not provided or not found", { function: "getProductCodes" });
    return "";
  }

  var data = sheet.getDataRange().getValues();

  if (data.length < 2) {
    logWarn("Sheet has no data rows", { sheetName: sheet.getName() });
    return "";
  }

  var headers = data[0];
  var columnIndex = getColumnIndex(headers, "product.code", false, sheet.getName());

  if (columnIndex === -1) {
    logWarn("Column 'product.code' not found in sheet", { sheetName: sheet.getName() });
    return "";
  }

  var values = data.slice(1)
                  .map(row => row[columnIndex])
                  .filter(Boolean)
                  .join(",");

  logDebug("Extracted product codes", {
    count: values.split(",").length,
    sheetName: sheet.getName()
  });

  return values;
}