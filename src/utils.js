// Original getConfiguration function is now in config.js
// This file now contains utility functions for the UI and other operations

/**
 * Converts an error from the new apiGet structure to a standardized format for UI display
 * @param {Error} error - The error object from apiGet
 * @param {string} column - The column name associated with this error
 * @return {Object} Standardized error object for UI display
 */
function formatApiErrorForDisplay(error, column) {
  var errorType = "Unknown Error";
  if (error.isApiError) {
    errorType = "API Error";
  } else if (error.isNetworkError) {
    errorType = "Network Error";
  } else if (error.isParseError) {
    errorType = "Parse Error";
  }

  return {
    column: column,
    status: error.statusCode || 500,
    error: errorType,
    message: error.message,
    response: error.response || error.toString()
  };
}

/**
 * Exibe uma mensagem de erro técnica para o usuário
 * @param {string} title - Título da mensagem de erro
 * @param {string|Error|Object} error - Mensage<PERSON> de erro, objeto Error ou resposta da API
 * @param {Object} [details] - Detalhes adicionais do erro
 */
function showErrorDialog(title, error, details) {
  var errorMessage = "";

  // Formata o título (não duplica se já for sobre erros de API)
  errorMessage += "<b>" + title + "</b><br><br>";

  // Verifica se há erros de API para exibir primeiro
  if (details && details.apiErrors && details.apiErrors.length > 0) {
    // Não adiciona "Erros de API:" se o título já contém essa informação
    if (title.indexOf("Erros na API") === -1) {
      errorMessage += "<b>Erros de API:</b><br>";
    }

    for (var i = 0; i < details.apiErrors.length; i++) {
      var apiError = details.apiErrors[i];
      errorMessage += "<hr>";
      errorMessage += "<b>Coluna:</b> " + apiError.column + "<br>";
      errorMessage += "<b>Status:</b> " + apiError.status + "<br>";
      errorMessage += "<b>Erro:</b> " + apiError.error + "<br>";
      errorMessage += "<b>Mensagem:</b> " + apiError.message + "<br>";

      if (apiError.response) {
        errorMessage += "<b>Resposta completa:</b><br>";
        // Tenta analisar a resposta se for uma string JSON
        if (typeof apiError.response === 'string' && apiError.response.startsWith('{')) {
          try {
            var parsedResponse = JSON.parse(apiError.response);
            errorMessage += "<pre>" + JSON.stringify(parsedResponse, null, 2) + "</pre><br>";
          } catch (e) {
            // Se falhar ao analisar, usa a string original
            errorMessage += "<pre>" + apiError.response + "</pre><br>";
          }
        } else {
          errorMessage += "<pre>" + JSON.stringify(apiError.response, null, 2) + "</pre><br>";
        }
      }
    }

    errorMessage += "<hr><br>";
  }

  // Adiciona a mensagem de erro principal
  if (error instanceof Error) {
    errorMessage += "<b>Erro:</b> " + error.message + "<br>";
    if (error.stack) {
      errorMessage += "<br><b>Stack trace:</b><br><pre>" + error.stack + "</pre><br>";
    }
  } else if (typeof error === 'object') {
    errorMessage += "<b>Resposta da API:</b><br><pre>" + JSON.stringify(error, null, 2) + "</pre><br>";
  } else {
    errorMessage += "<b>Erro:</b> " + error + "<br>";
  }

  // Adiciona outros detalhes se fornecidos (exceto apiErrors que já foi tratado)
  if (details) {
    var otherDetails = {};
    var hasOtherDetails = false;

    for (var key in details) {
      if (details.hasOwnProperty(key) && key !== 'apiErrors') {
        otherDetails[key] = details[key];
        hasOtherDetails = true;
      }
    }

    if (hasOtherDetails) {
      errorMessage += "<br><b>Detalhes adicionais:</b><br>";
      for (var key in otherDetails) {
        if (typeof otherDetails[key] === 'object') {
          errorMessage += key + ":<br><pre>" + JSON.stringify(otherDetails[key], null, 2) + "</pre><br>";
        } else {
          errorMessage += key + ": " + otherDetails[key] + "<br>";
        }
      }
    }
  }

  // Cria e exibe o HTML
  var htmlOutput = HtmlService
    .createHtmlOutput(errorMessage)
    .setWidth(700)
    .setHeight(500);

  SpreadsheetApp.getUi().showModalDialog(htmlOutput, "Erro na Operação");
}

/**
 * Creates the custom menu when the spreadsheet is opened
 */
function onOpen() {
  var ui = SpreadsheetApp.getUi();
  ui.createMenu('Integration Options')
    .addItem("[Upsert] Price List Items", "handlePriceListItemsUpsert")
    .addItem("[Upsert] Customers", "handleCustomersUpsert")
    .addSeparator()
    .addItem("Generate and Download CSV", "downloadReplacedCSV")
    .addSeparator()
    .addItem("Clean Generated Sheets", "cleanGeneratedSheets")
    .addSeparator()
    .addItem("Open Upsert Import Page", "openUpsertPage")
    .addItem("Open Delete Import Page", "openDeletePage")
    .addToUi();

  logInfo("Custom menu created");
}

/**
 * Opens the upsert importation page in a new browser tab
 */
function openUpsertPage() {
  try {
    var url = getConfiguration("upsert_importation_url");
    actionOpenPage(url, "Upsert Import Page");
  } catch (e) {
    logException("Failed to open upsert page", e, { pageTitle: "Upsert Import Page" });
    showErrorDialog("Falha ao Abrir Página de Upsert", e);
  }
}

/**
 * Opens the delete importation page in a new browser tab
 */
function openDeletePage() {
  try {
    var url = getConfiguration("delete_importation_url");
    actionOpenPage(url, "Delete Import Page");
  } catch (e) {
    logException("Failed to open delete page", e, { pageTitle: "Delete Import Page" });
    showErrorDialog("Falha ao Abrir Página de Delete", e);
  }
}

/**
 * Opens a URL in a new browser tab
 * @param {string} url - The URL to open
 * @param {string} pageTitle - The title of the page (for logging)
 */
function actionOpenPage(url, pageTitle) {
  if (!url) {
    logWarn("URL not configured for " + pageTitle);
    SpreadsheetApp.getUi().alert("URL not configured.");
    return;
  }
  logInfo("Opening page: " + pageTitle + " - URL: " + url);

  var html = HtmlService.createHtmlOutput(`
    <script>
      window.open("${url}", "_blank");
      google.script.host.close();
    </script>
  `)
  .setWidth(300)
  .setHeight(80);

  SpreadsheetApp.getUi().showModalDialog(html, "Opening page...");
}

/**
 * Handles the upsert operation for price list items
 */
function handlePriceListItemsUpsert() {
  var sheetName = "priceListItems";
  try {
    handleUpsert(sheetName);
    SpreadsheetApp.getUi().alert("Price list items upsert completed successfully.");
  } catch (e) {
    if (e.name === "HandledError") {
      logInfo("Operation for " + sheetName + " was handled and dialog shown by handleUpsert. Original error message: " + e.message);
      // Error already displayed to user by handleUpsert. Success message is skipped.
    } else {
      logException("Unexpected error during " + sheetName + " upsert", e, { sheetName: sheetName });
      showErrorDialog("Falha na Operação de Upsert (" + sheetName + ")", e, { sheetName: sheetName });
    }
  }
}

/**
 * Handles the upsert operation for customers
 */
function handleCustomersUpsert() {
  var sheetName = "customers";
  try {
    handleUpsert(sheetName);
    SpreadsheetApp.getUi().alert("Customers upsert completed successfully.");
  } catch (e) {
    if (e.name === "HandledError") {
      logInfo("Operation for " + sheetName + " was handled and dialog shown by handleUpsert. Original error message: " + e.message);
    } else {
      logException("Unexpected error during " + sheetName + " upsert", e, { sheetName: sheetName });
      showErrorDialog("Falha na Operação de Upsert (" + sheetName + ")", e, { sheetName: sheetName });
    }
  }
}

/**
 * Limpa as planilhas geradas durante as operações de upsert
 * Identifica planilhas com nomes como "customers_YYYYMMDD" e "priceListItems_YYYYMMDD"
 */
function cleanGeneratedSheets() {
  try {
    var ss = SpreadsheetApp.getActiveSpreadsheet();
    var sheets = ss.getSheets();
    var sheetsToDelete = [];
    // Padrão para identificar planilhas geradas (nome_YYYYMMDD)
    var datePattern = /^(customers|priceListItems)_\d{8}$/;

    // Identifica as planilhas geradas
    for (var i = 0; i < sheets.length; i++) {
      var sheetName = sheets[i].getName();
      if (datePattern.test(sheetName)) {
        sheetsToDelete.push({
          sheet: sheets[i],
          name: sheetName
        });
      }
    }

    // Se não houver planilhas para excluir, informa o usuário
    if (sheetsToDelete.length === 0) {
      SpreadsheetApp.getUi().alert("Nenhuma planilha gerada foi encontrada para limpeza.");
      return;
    }

    // Pede confirmação ao usuário antes de excluir
    var ui = SpreadsheetApp.getUi();
    var sheetNames = sheetsToDelete.map(function(item) { return item.name; }).join("\n");
    var response = ui.alert(
      "Confirmar limpeza",
      "As seguintes planilhas serão excluídas:\n\n" + sheetNames + "\n\nDeseja continuar?",
      ui.ButtonSet.YES_NO
    );

    // Se o usuário confirmar, exclui as planilhas
    if (response === ui.Button.YES) {
      for (var i = 0; i < sheetsToDelete.length; i++) {
        ss.deleteSheet(sheetsToDelete[i].sheet);
      }
      ui.alert("Limpeza concluída", "Foram excluídas " + sheetsToDelete.length + " planilhas geradas.", ui.ButtonSet.OK);
    }
  } catch (e) {
    logException("Erro ao limpar planilhas geradas", e);
    showErrorDialog("Erro ao Limpar Planilhas Geradas", e);
  }
}