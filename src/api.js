/**
 * Centralized API client for making requests to the TOTVS CRM API
 */

/**
 * Makes a GET request to the TOTVS CRM API
 * @param {string} service - The integration service (e.g., "product", "customer")
 * @param {string} resource - The integration resource (e.g., "products-integration")
 * @param {string} version - The integration version (e.g., "v1", "v2")
 * @param {string} filter - The integration filter
 * @param {boolean} [showErrors=true] - Whether to show error dialogs to the user
 * @return {Object} The parsed response from the API
 * @throws {ApiError} If the API returns an error
 */
function apiGet(service, resource, version, filter, showErrors) {
  // Default showErrors to false - erros serão tratados centralmente
  if (showErrors === undefined) showErrors = false;

  const authToken = `Bearer ${getConfiguration("auth_token")}`;
  const url = getConfiguration("api_base_url") || "https://totvscrm.app/api/v1/integration/record";
  const headers = {
    "Content-Type": "application/json",
    "Authorization": authToken,
    "Integration-Service": service,
    "Integration-Resource": resource,
    "Integration-Version": version,
    "Integration-Filter": filter
  };
  const options = {
    "method": "get",
    "headers": headers,
    "muteHttpExceptions": true
  };

  console.log("apiGet - Request URL: " + url);
  console.log("apiGet - Request Options: " + JSON.stringify(options));

  try {
    const response = UrlFetchApp.fetch(url, options);
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    console.log("apiGet - Response Code: " + responseCode);
    console.log("apiGet - Response Text: " + responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''));

    // Verifica se a resposta é um erro
    if (responseCode >= 400) {
      var errorDetails = {
        service: service,
        resource: resource,
        version: version,
        responseCode: responseCode
      };

      try {
        var parsedError = JSON.parse(responseText);

        // Cria um erro personalizado com detalhes da API
        var apiError = new Error("API Error: " + (parsedError.message || "Unknown error"));
        apiError.apiResponse = parsedError;
        apiError.statusCode = responseCode;
        apiError.details = errorDetails;
        apiError.isApiError = true;

        // Exibe o erro para o usuário se solicitado
        if (showErrors) {
          showErrorDialog("Erro na API: " + resource, parsedError, errorDetails);
        }

        throw apiError;
      } catch (parseError) {
        // Se não conseguir analisar a resposta como JSON, usa o texto bruto
        var apiError = new Error("API Error: Status " + responseCode);
        apiError.apiResponse = responseText;
        apiError.statusCode = responseCode;
        apiError.details = errorDetails;
        apiError.isApiError = true;

        if (showErrors) {
          showErrorDialog("Erro na API: Código " + responseCode,
                         responseText,
                         errorDetails);
        }

        throw apiError;
      }
    }

    // Processa a resposta bem-sucedida
    try {
      return JSON.parse(responseText);
    } catch (e) {
      console.error("apiGet: Failed to parse JSON response: " + e.message + ". Response: " + responseText);

      if (showErrors) {
        showErrorDialog("Erro ao processar resposta da API",
                       e,
                       { response: responseText });
      }

      throw new Error("Failed to parse API response: " + e.message);
    }
  } catch (e) {
    // Captura erros de rede ou outros erros não relacionados à resposta da API
    if (e.apiResponse) {
      // Este é um erro de API que já foi tratado acima
      throw e;
    } else {
      console.error("apiGet: Error fetching from API: " + e.message);

      if (showErrors) {
        showErrorDialog("Erro de conexão com a API",
                       e,
                       { service: service, resource: resource });
      }

      throw new Error("API request failed: " + e.message);
    }
  }
}

/**
 * Processes API response to standardize the return format
 * @param {Object} parsedResponse - The parsed response from the API
 * @param {Function} filterFn - Function to filter valid items
 * @param {string} logPrefix - Prefix for log messages
 * @param {boolean} [markApiError=false] - Whether to mark API errors in the active sheet
 * @return {Object} Standardized response with items array
 */
function processApiResponse(parsedResponse, filterFn, logPrefix, markApiError) {
  var itemsToReturn = [];

  // Verifica se a resposta é um erro de API (status >= 400)
  if (parsedResponse && parsedResponse.status && parsedResponse.status >= 400) {
    console.warn(logPrefix + ": API returned error status " + parsedResponse.status + ". Message: " +
                (parsedResponse.message || "Unknown error"));

    // Marca o erro na planilha ativa se solicitado
    if (markApiError) {
      try {
        var sheet = SpreadsheetApp.getActiveSpreadsheet().getActiveSheet();
        var lastColumn = sheet.getLastColumn();

        // Adiciona uma coluna de erro se não existir
        if (sheet.getRange(1, lastColumn + 1).getValue() !== "API_ERROR") {
          sheet.getRange(1, lastColumn + 1).setValue("API_ERROR");
          sheet.getRange(1, lastColumn + 1).setBackground("#ffcccc");
        }

        // Adiciona detalhes do erro
        sheet.getRange(2, lastColumn + 1).setValue(JSON.stringify({
          status: parsedResponse.status,
          error: parsedResponse.error,
          message: parsedResponse.message
        }));
      } catch (e) {
        console.error("Failed to mark API error in sheet: " + e.message);
      }
    }

    return {
      items: [],
      error: true,
      status: parsedResponse.status,
      message: parsedResponse.message || "Unknown error",
      error_details: parsedResponse
    };
  }

  // Processa a resposta normal
  if (Array.isArray(parsedResponse)) {
    itemsToReturn = parsedResponse.filter(filterFn);
  } else if (parsedResponse && Array.isArray(parsedResponse.items)) {
    itemsToReturn = parsedResponse.items.filter(filterFn);
  } else if (parsedResponse && filterFn(parsedResponse)) {
    itemsToReturn = [parsedResponse];
  } else {
    console.warn(logPrefix + ": API response did not contain valid items. Response: " + JSON.stringify(parsedResponse));
  }

  console.log(logPrefix + " - Items to return: " + JSON.stringify({ items: itemsToReturn }));
  return { items: itemsToReturn };
}

/**
 * Makes a POST request to the TOTVS CRM API for upsert operations
 * @param {Object} payload - The payload to send
 * @param {string} url - The URL to send the request to
 * @return {Object} The response from the API
 */
function apiPost(payload, url) {
  if (!url) {
    throw new Error("URL not provided for API POST request");
  }

  const options = {
    "method": "post",
    "headers": payload.headers,
    "payload": JSON.stringify(payload),
    "muteHttpExceptions": true
  };

  console.log(`apiPost - Sending to URL: ${url}`);
  console.log(`apiPost - Options: ${JSON.stringify(options)}`);

  try {
    const response = UrlFetchApp.fetch(url, options);
    const statusCode = response.getResponseCode();
    const responseBody = response.getContentText();

    console.log(`apiPost - Response: Status ${statusCode}, Body: ${responseBody.substring(0, 500)}${responseBody.length > 500 ? '...' : ''}`);

    return {
      statusCode: statusCode,
      body: responseBody
    };
  } catch (e) {
    console.error(`Error during API POST request: ${e.message}`);
    throw new Error(`API POST request failed: ${e.message}`);
  }
}
