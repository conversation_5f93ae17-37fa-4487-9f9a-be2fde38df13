/**
 * Centralized API client for making requests to the TOTVS CRM API
 */

/**
 * Makes a GET request to the TOTVS CRM API
 * @param {string} service - The integration service (e.g., "product", "customer")
 * @param {string} resource - The integration resource (e.g., "products-integration")
 * @param {string} version - The integration version (e.g., "v1", "v2")
 * @param {string} filter - The integration filter
 * @return {Object} The parsed response from the API
 * @throws {Error} If the API returns an error or network/parsing fails. Error object will have `isApiError`, `isNetworkError`, or `isParseError` flags.
 */
function apiGet(service, resource, version, filter) {
  const authToken = `Bearer ${getConfiguration("auth_token")}`;
  const url = getConfiguration("api_base_url") || "https://totvscrm.app/api/v1/integration/record";
  const headers = {
    "Content-Type": "application/json",
    "Authorization": authToken,
    "Integration-Service": service,
    "Integration-Resource": resource,
    "Integration-Version": version,
    "Integration-Filter": filter
  };
  const options = {
    "method": "get",
    "headers": headers,
    "muteHttpExceptions": true
  };

  console.log("apiGet - Request URL: " + url);
  console.log("apiGet - Request Options: " + JSON.stringify(options));

  let httpResponse;
  try {
    httpResponse = UrlFetchApp.fetch(url, options);
  } catch (e) {
    console.error(`apiGet: Network error or UrlFetchApp failed for ${resource}: ${e.message}`);
    const networkError = new Error(`Network error while fetching ${resource}: ${e.message}`);
    networkError.isNetworkError = true;
    networkError.statusCode = 0; // Indicate non-HTTP error
    networkError.details = { service, resource, version, originalError: e.message };
    networkError.response = e.toString();
    throw networkError;
  }

  const responseCode = httpResponse.getResponseCode();
  const responseText = httpResponse.getContentText();

  console.log(`apiGet - Response Code for ${resource}: ${responseCode}`);
  console.log(`apiGet - Response Text for ${resource} (first 500 chars): ${responseText.substring(0, 500)}` + (responseText.length > 500 ? '...' : ''));

  if (responseCode >= 400) {
    console.warn(`apiGet: API error for ${resource}. Status: ${responseCode}`);
    let parsedErrorBody;
    try {
      parsedErrorBody = JSON.parse(responseText);
    } catch (parseErr) {
      parsedErrorBody = responseText; // Use raw text if not JSON
    }

    const apiErrorMessage = (typeof parsedErrorBody === 'object' && parsedErrorBody && parsedErrorBody.message) ?
                            `API Error (${resource}): ${parsedErrorBody.message}` :
                            `API Error (${resource}): Status ${responseCode}`;
    const apiError = new Error(apiErrorMessage);
    apiError.isApiError = true;
    apiError.statusCode = responseCode;
    apiError.response = parsedErrorBody;
    apiError.details = { service, resource, version, responseCode };
    throw apiError;
  }

  try {
    return JSON.parse(responseText);
  } catch (e) {
    console.error(`apiGet: Failed to parse JSON response for ${resource}: ${e.message}. Response (first 500 chars): ${responseText.substring(0,500)}`);
    const parseError = new Error(`Failed to parse API response for ${resource}: ${e.message}`);
    parseError.isParseError = true;
    parseError.statusCode = responseCode; // Include status code for context
    parseError.response = responseText;
    parseError.details = { service, resource, version };
    throw parseError;
  }
}

/**
 * Processes API response to standardize the return format
 * @param {Object} parsedResponse - The parsed response from the API
 * @param {Function} filterFn - Function to filter valid items
 * @param {string} logPrefix - Prefix for log messages
 * @return {Object} Standardized response with items array
 */
function processApiResponse(parsedResponse, filterFn, logPrefix) {
  var itemsToReturn = [];

  // Assumes parsedResponse is from a successful (2xx) API call,
  // as apiGet now throws an error for HTTP status >= 400.
  if (Array.isArray(parsedResponse)) {
    itemsToReturn = parsedResponse.filter(filterFn);
  } else if (parsedResponse && Array.isArray(parsedResponse.items)) {
    itemsToReturn = parsedResponse.items.filter(filterFn);
  } else if (parsedResponse && filterFn(parsedResponse)) {
    itemsToReturn = [parsedResponse];
  } else {
    console.warn(logPrefix + ": API response did not contain valid items or items did not match filter. Response (first 500 chars): " + JSON.stringify(parsedResponse).substring(0, 500));
  }

  console.log(logPrefix + " - Number of items to return: " + itemsToReturn.length);
  return { items: itemsToReturn };
}

/**
 * Makes a POST request to the TOTVS CRM API for upsert operations
 * @param {Object} payload - The payload to send
 * @param {string} url - The URL to send the request to
 * @return {Object} The response from the API
 */
function apiPost(payload, url) {
  if (!url) {
    throw new Error("URL not provided for API POST request");
  }

  const options = {
    "method": "post",
    "headers": payload.headers,
    "payload": JSON.stringify(payload),
    "muteHttpExceptions": true
  };

  console.log(`apiPost - Sending to URL: ${url}`);
  console.log(`apiPost - Options: ${JSON.stringify(options)}`);

  try {
    const response = UrlFetchApp.fetch(url, options);
    const statusCode = response.getResponseCode();
    const responseBody = response.getContentText();

    console.log(`apiPost - Response: Status ${statusCode}, Body: ${responseBody.substring(0, 500)}${responseBody.length > 500 ? '...' : ''}`);

    return {
      statusCode: statusCode,
      body: responseBody
    };
  } catch (e) {
    console.error(`Error during API POST request: ${e.message}`);
    throw new Error(`API POST request failed: ${e.message}`);
  }
}
