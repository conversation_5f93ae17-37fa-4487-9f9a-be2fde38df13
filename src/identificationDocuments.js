/**
 * Gets identification documents from the API
 * @param {Sheet} sheet - The sheet to get identification document codes from
 * @return {Object} Object with items array containing identification documents
 */
function getIdentificationDocuments(sheet) {
    if (!sheet) {
        logWarn("Sheet not provided", { function: "getIdentificationDocuments" });
        return { items: [] };
    }

    var identificationDocumentAcronyms = getIdentificationDocumentAcronyms(sheet);

    if (!identificationDocumentAcronyms) {
        logWarn("No identification document acronyms found in sheet to fetch", { sheetName: sheet.getName() });
        return { items: [] };
    }

    try {
        const integrationFilter = `{identificationDocumentAcronym->in->[${identificationDocumentAcronyms}]}{select->[identificationDocumentId,identificationDocumentAcronym]}{pageSize->1000}`;
        const parsedResponse = apiGet(
            "customer",
            "customer-identification-documents-integration",
            "v1",
            integrationFilter
        );

        // Log the response for debugging
        logDebug("Identification documents API response (raw)", { responsePreview: JSON.stringify(parsedResponse).substring(0, 500) });

        const result = processApiResponse(
            parsedResponse,
            item => item && typeof item.identificationDocumentAcronym !== 'undefined' && typeof item.identificationDocumentId !== 'undefined',
            "getIdentificationDocuments"
        );

        // Log the processed items
        console.log("Processed identification documents:",
                   result.items.length > 0 ?
                   JSON.stringify(result.items.slice(0, 3)) :
                   "No valid items found");

        return result;
    } catch (e) {
        logException("Error fetching identification documents", e, { identificationDocumentAcronyms });
        throw e; // Propagate error with new structure
    }
}

/**
 * Gets identification document acronyms from a sheet
 * @param {Sheet} sheet - The sheet to get identification document acronyms from
 * @return {string} Comma-separated list of identification document acronyms
 */
function getIdentificationDocumentAcronyms(sheet) {
    if (!sheet) {
        logWarn("Sheet not provided", { function: "getIdentificationDocumentAcronyms" });
        return "";
    }

    var data = sheet.getDataRange().getValues();

    if (data.length < 2) {
        logWarn("Sheet has no data rows", { sheetName: sheet.getName() });
        return "";
    }

    var headers = data[0];
    // Tenta encontrar a coluna com o nome correto
    var columnIndex = headers.indexOf("customerIdentificationDocuments[0].identificationDocumentAcronym");

    // Se não encontrar, tenta o nome alternativo
    if (columnIndex === -1) {
        columnIndex = headers.indexOf("customerIdentificationDocuments[0].identificationDocument.code");
        logInfo("Usando nome alternativo para a coluna de acrônimo do documento de identificação: customerIdentificationDocuments[0].identificationDocument.code");
    }

    if (columnIndex === -1) {
        logWarn("Nenhuma coluna de acrônimo de documento de identificação encontrada.", { triedColumns: ["customerIdentificationDocuments[0].identificationDocumentAcronym", "customerIdentificationDocuments[0].identificationDocument.code"] });
    }

    if (columnIndex === -1) {
        logWarn("Column 'customerIdentificationDocuments[0].identificationDocument.code' not found in sheet",
            { sheetName: sheet.getName() });
        return "";
    }

    var uniqueAcronyms = new Set();
    for (var i = 1; i < data.length; i++) {
        var acronym = data[i][columnIndex];
        if (acronym && acronym.toString().trim() !== "") {
            uniqueAcronyms.add(acronym.toString().trim());
        }
    }

    var acronymsArray = Array.from(uniqueAcronyms);
    logDebug("Extracted identification document acronyms", {
        count: acronymsArray.length,
        acronyms: acronymsArray.join(","),
        sheetName: sheet.getName()
    });

    return acronymsArray.join(",");
}
