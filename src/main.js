/**
 * Main entry point for the application
 * This file loads all dependencies and initializes the application
 */

/**
 * Initializes the application
 * This is called automatically when the spreadsheet is opened
 */
function initialize() {
  try {
    // Set up logging
    setLogLevel(LOG_LEVEL_INFO); // Use the variable instead of the constant
    console.log("Application initializing");

    // Initialize configuration cache
    initConfigCache();

    // Create custom menu
    onOpen();

    console.log("Application initialized successfully");
  } catch (e) {
    console.error("Failed to initialize application: " + e.message);
    if (e.stack) {
      console.error("Stack trace: " + e.stack);
    }
  }
}

/**
 * Downloads a CSV file with replaced values
 * This is a placeholder function that should be implemented
 */
function downloadReplacedCSV() {
  try {
    // This is a placeholder for the CSV download functionality
    // Implement this based on your requirements
    SpreadsheetApp.getUi().alert("CSV download functionality not implemented yet.");
    console.warn("CSV download functionality not implemented");
  } catch (e) {
    console.error("CSV download failed: " + e.message);
    if (e.stack) {
      console.error("Stack trace: " + e.stack);
    }
    SpreadsheetApp.getUi().alert("Error: " + e.message);
  }
}

/**
 * Runs a health check on the application
 * Verifies that all required configurations are present and API is accessible
 * @return {Object} Health check results
 */
function runHealthCheck() {
  const results = {
    configurationCheck: false,
    apiCheck: false,
    sheetsCheck: false,
    errors: []
  };

  try {
    // Check configuration
    try {
      // Verify that required configuration values exist by trying to access them
      getConfiguration("auth_token"); // This will throw an error if not found
      const apiBaseUrl = getConfiguration("api_base_url") || "https://totvscrm.app/api/v1/integration/record";
      results.configurationCheck = true;
      console.log("Configuration check passed - API URL: " + apiBaseUrl);
    } catch (e) {
      results.errors.push("Configuration check failed: " + e.message);
      console.error("Configuration check failed: " + e.message);
    }

    // Check sheets
    try {
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      const requiredSheets = ["configuration", "priceListItems", "customers"];
      const missingSheets = [];

      for (const sheetName of requiredSheets) {
        if (!ss.getSheetByName(sheetName)) {
          missingSheets.push(sheetName);
        }
      }

      if (missingSheets.length === 0) {
        results.sheetsCheck = true;
        console.log("Sheets check passed");
      } else {
        results.errors.push("Missing required sheets: " + missingSheets.join(", "));
        console.warn("Sheets check failed - Missing sheets: " + missingSheets.join(", "));
      }
    } catch (e) {
      results.errors.push("Sheets check failed: " + e.message);
      console.error("Sheets check failed: " + e.message);
    }

    // Check API access
    try {
      // Make a simple API call to check connectivity
      const testFilter = "{pageSize->1}";
      apiGet("product", "products-integration", "v2", testFilter);
      results.apiCheck = true;
      console.log("API check passed");
    } catch (e) {
      results.errors.push("API check failed: " + e.message);
      console.error("API check failed: " + e.message);
    }

    return results;
  } catch (e) {
    console.error("Health check failed: " + e.message);
    if (e.stack) {
      console.error("Stack trace: " + e.stack);
    }
    results.errors.push("Health check failed: " + e.message);
    return results;
  }
}
