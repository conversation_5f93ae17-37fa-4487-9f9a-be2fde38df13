/**
 * Main entry point for the application
 * This file loads all dependencies and initializes the application
 */

/**
 * Initializes the application
 * This is called automatically when the spreadsheet is opened
 */
function initialize() {
  try {
    // Set up logging
    setLogLevel(LOG_LEVEL_INFO); // Use the variable instead of the constant
    logInfo("Application initializing");

    // Initialize configuration cache
    initConfigCache();

    // Create custom menu
    onOpen();
    logInfo("Application initialized successfully");
  } catch (e) {
    logException("Failed to initialize application", e);
  }
}

/**
 * Downloads a CSV file with replaced values
 * This is a placeholder function that should be implemented
 */
function downloadReplacedCSV() {
  try {
    // This is a placeholder for the CSV download functionality
    // Implement this based on your requirements
    SpreadsheetApp.getUi().alert("CSV download functionality not implemented yet.");
    logWarn("CSV download functionality not implemented");
  } catch (e) {
    logException("CSV download failed", e);
    showErrorDialog("Falha no Download do CSV", e);
  }
}

/**
 * Runs a health check on the application
 * Verifies that all required configurations are present and API is accessible
 * @return {Object} Health check results
 */
function runHealthCheck() {
  const results = {
    configurationCheck: false,
    apiCheck: false,
    sheetsCheck: false,
    errors: []
  };

  try {
    // Check configuration
    try {
      // Verify that required configuration values exist by trying to access them
      getConfiguration("auth_token"); // This will throw an error if not found
      const apiBaseUrl = getConfiguration("api_base_url") || "https://totvscrm.app/api/v1/integration/record";
      results.configurationCheck = true;
      logInfo("Configuration check passed - API URL: " + apiBaseUrl);
    } catch (e) {
      results.errors.push("Configuration check failed: " + e.message);
      logError("Configuration check failed: " + e.message);
    }

    // Check sheets
    try {
      const ss = SpreadsheetApp.getActiveSpreadsheet();
      const requiredSheets = ["configuration", "priceListItems", "customers"];
      const missingSheets = [];

      for (const sheetName of requiredSheets) {
        if (!ss.getSheetByName(sheetName)) {
          missingSheets.push(sheetName);
        }
      }

      if (missingSheets.length === 0) {
        results.sheetsCheck = true;
        logInfo("Sheets check passed");
      } else {
        results.errors.push("Missing required sheets: " + missingSheets.join(", "));
        logWarn("Sheets check failed - Missing sheets: " + missingSheets.join(", "));
      }
    } catch (e) {
      results.errors.push("Sheets check failed: " + e.message);
      logError("Sheets check failed: " + e.message);
    }

    // Check API access
    try {
      // Make a simple API call to check connectivity
      const testFilter = "{pageSize->1}";
      apiGet("product", "products-integration", "v2", testFilter);
      results.apiCheck = true;
      logInfo("API check passed");
    } catch (e) {
      results.errors.push("API check failed: " + e.message);
      logError("API check failed: " + e.message);
    }

    return results;
  } catch (e) {
    logException("Health check failed", e);
    results.errors.push("Health check failed: " + e.message);
    return results;
  }
}
