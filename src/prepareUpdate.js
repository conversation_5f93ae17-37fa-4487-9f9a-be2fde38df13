function prepareUpdate(sheet) {
    var apiData = getPriceListItems(sheet);
    var data = sheet.getDataRange().getValues();

    if (data.length < 2) {
        return;
    }

    var headers = data[0];

    var productIndex = headers.indexOf("product.id");
    var priceListIndex = headers.indexOf("priceList.id");
    var updateIdIndex = headers.indexOf("integrationFilterId");

    if (productIndex === -1 || priceListIndex === -1) {
        return;
    }

    if (updateIdIndex === -1) {
        // Adiciona a coluna integrationFilterId no início (à esquerda)
        sheet.insertColumnBefore(1);
        sheet.getRange(1, 1).setValue("integrationFilterId");
        updateIdIndex = 0;

        // Atualiza os índices das colunas após a inserção
        productIndex++;
        priceListIndex++;
    }

    var apiMap = new Map();
    if (apiData && Array.isArray(apiData.items)) {
        apiData.items.forEach(item => {
            if (item && item.product && typeof item.product.id !== 'undefined' &&
                item.priceList && typeof item.priceList.id !== 'undefined' &&
                typeof item.id !== 'undefined') {
                var key = `${item.product.id}_${item.priceList.id}`;
                apiMap.set(key, item.id);
            }
        });
    } else {
        logWarn("prepareUpdate: apiData.items não é um array válido.", { apiData: apiData });
    }

    for (var i = 1; i < data.length; i++) {
        var productId = data[i][productIndex];
        var priceListId = data[i][priceListIndex];

        if (productId && priceListId) {
            var key = `${productId}_${priceListId}`;
            if (apiMap.has(key)) {
                // Como updateIdIndex agora é 0, usamos a coluna 1 (primeira coluna)
                sheet.getRange(i + 1, updateIdIndex + 1).setValue(apiMap.get(key));
            }
        }
    }
}