/**
 * Gets price lists from the API based on price list codes in the sheet
 * @param {Sheet} [sheet] - Optional sheet to get price list codes from (defaults to "priceListItems" sheet)
 * @return {Object} Object containing items array with price list data
 */
function getPriceLists(sheet) {
    // If sheet is not provided, get it from the active spreadsheet
    if (!sheet) {
        try {
            sheet = validateSheet("priceListItems");
        } catch (e) {
            logError("Failed to get priceListItems sheet", { error: e.message });
            return { items: [] };
        }
    }

    var priceListCodesString = getPriceListCodes(sheet);

    if (!priceListCodesString) {
        logWarn("No price list codes found in sheet to fetch", { sheetName: sheet.getName() });
        return { items: [] };
    }

    try {
        const integrationFilter = `{code->in->[${priceListCodesString}]}{select->[id,code]}{pageSize->1000}`;
        const parsedResponse = apiGet(
            "product",
            "price-lists-integration",
            "v1",
            integrationFilter
        );

        return processApiResponse(
            parsedResponse,
            item => item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined',
            "getPriceLists"
        );
    } catch (e) {
        logException("Error fetching price lists", e, { priceListCodesString });
        return { items: [] };
    }
}

/**
 * Gets price list codes from a sheet
 * @param {Sheet} sheet - The sheet to get price list codes from
 * @return {string} Comma-separated list of price list codes
 */
function getPriceListCodes(sheet) {
  if (!sheet) {
    logWarn("Sheet not provided or not found", { function: "getPriceListCodes" });
    return "";
  }

  var data = sheet.getDataRange().getValues();

  if (data.length < 2) {
    logWarn("Sheet has no data rows", { sheetName: sheet.getName() });
    return "";
  }

  var headers = data[0];
  var columnIndex = getColumnIndex(headers, "priceList.code", false, sheet.getName());

  if (columnIndex === -1) {
    logWarn("Column 'priceList.code' not found in sheet", { sheetName: sheet.getName() });
    return "";
  }

  var values = data.slice(1)
                  .map(row => row[columnIndex])
                  .filter(Boolean)
                  .join(",");

  logDebug("Extracted price list codes", {
    count: values.split(",").length,
    sheetName: sheet.getName()
  });

  return values;
}

/**
 * Gets price list items from the API based on price list IDs in the sheet
 * @param {Sheet} sheet - The sheet to get price list IDs from
 * @return {Object} Object containing items array with price list item data
 */
function getPriceListItems(sheet) {
    if (!sheet) {
        try {
            sheet = validateSheet("priceListItems");
        } catch (e) {
            logError("Failed to get priceListItems sheet", { error: e.message });
            return { items: [] };
        }
    }

    const priceListIds = getPriceListIds(sheet);
    if (!priceListIds) {
        logWarn("No price list IDs found in sheet to fetch items", { sheetName: sheet.getName() });
        return { items: [] };
    }

    try {
        const integrationFilter = `{priceList.id->in->[${priceListIds}]}{select->[id,product.id,priceList.id]}{pageSize->1000}`;
        const parsedResponse = apiGet(
            "product",
            "price-list-items-integration",
            "v1",
            integrationFilter
        );

        // Define a filter function for valid price list items
        const filterValidItems = item =>
            item &&
            item.product &&
            typeof item.product.id !== 'undefined' &&
            item.priceList &&
            typeof item.priceList.id !== 'undefined' &&
            typeof item.id !== 'undefined';

        return processApiResponse(
            parsedResponse,
            filterValidItems,
            "getPriceListItems"
        );
    } catch (e) {
        logException("Error fetching price list items", e, { priceListIds });
        return { items: [] };
    }
}

/**
 * Gets price list IDs from a sheet
 * @param {Sheet} sheet - The sheet to get price list IDs from
 * @return {string} Comma-separated list of price list IDs
 */
function getPriceListIds(sheet) {
  if (!sheet) {
    logWarn("Sheet not provided", { function: "getPriceListIds" });
    return "";
  }

  var data = sheet.getDataRange().getValues();

  if (data.length < 2) {
    logWarn("Sheet has no data rows", { sheetName: sheet.getName() });
    return "";
  }

  var headers = data[0];
  var columnIndex = getColumnIndex(headers, "priceList.id", false, sheet.getName());

  if (columnIndex === -1) {
    logWarn("Column 'priceList.id' not found in sheet. This may be expected if code replacement has not occurred yet.",
      { sheetName: sheet.getName() });
    return "";
  }

  // UUID regex pattern
  var uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  var values = data.slice(1)
                   .map(row => row[columnIndex])
                   .filter(id => id && uuidRegex.test(id))
                   .join(",");

  logDebug("Extracted price list IDs", {
    count: values.split(",").length,
    sheetName: sheet.getName()
  });

  return values;
}