/**
 * Gets customer person types from the API based on codes in the sheet
 * @param {Sheet} [sheet] - Optional sheet to get customer person type codes from (defaults to "customers" sheet)
 * @return {Object} Object containing items array with customer person type data
 */
function getCustomerPersonTypes(sheet) {
    // If sheet is not provided, get it from the active spreadsheet
    if (!sheet) {
        try {
            sheet = validateSheet("customers");
        } catch (e) {
            logError("Failed to get customers sheet", { error: e.message });
            return { items: [] };
        }
    }

    var customerPersonTypeCodesString = getCustomerPersonTypeCodes(sheet);

    if (!customerPersonTypeCodesString) {
        logWarn("No customer person type codes found in sheet to fetch", { sheetName: sheet.getName() });
        return { items: [] };
    }

    try {
        const integrationFilter = `{code->in->[${customerPersonTypeCodesString}]}{select->[id,code]}{pageSize->1000}`;
        const parsedResponse = apiGet(
            "customer",
            "customer-person-types-integration",
            "v1",
            integrationFilter
        );

        return processApiResponse(
            parsedResponse,
            item => item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined',
            "getCustomerPersonTypes"
        );
    } catch (e) {
        logException("Error fetching customer person types", e, { customerPersonTypeCodesString });
        return { items: [] };
    }
}

/**
 * Gets customer person type codes from a sheet
 * @param {Sheet} sheet - The sheet to get customer person type codes from
 * @return {string} Comma-separated list of customer person type codes
 */
function getCustomerPersonTypeCodes(sheet) {
  if (!sheet) {
    logWarn("Sheet not provided or not found", { function: "getCustomerPersonTypeCodes" });
    return "";
  }

  var data = sheet.getDataRange().getValues();

  if (data.length < 2) {
    logWarn("Sheet has no data rows", { sheetName: sheet.getName() });
    return "";
  }

  var headers = data[0];
  var columnIndex = getColumnIndex(headers, "customerPersonType.code", false, sheet.getName());

  if (columnIndex === -1) {
    logWarn("Column 'customerPersonType.code' not found in sheet", { sheetName: sheet.getName() });
    return "";
  }

  var values = data.slice(1)
                  .map(row => row[columnIndex])
                  .filter(Boolean)
                  .join(",");

  logDebug("Extracted customer person type codes", {
    count: values.split(",").length,
    sheetName: sheet.getName()
  });

  return values;
}