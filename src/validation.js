/**
 * Validation module for data validation functions
 */

/**
 * Validates required columns in a sheet
 * @param {Sheet} sheet - The sheet to validate
 * @param {Array} requiredColumns - Array of required column names
 * @return {Array} Array of validation errors
 */
function validateRequiredColumns(sheet, requiredColumns) {
  if (!requiredColumns || requiredColumns.length === 0) {
    return [];
  }

  var data = sheet.getDataRange().getValues();
  if (data.length < 2 && requiredColumns.length > 0) {
    throw new Error(`Sheet '${sheet.getName()}' does not have enough data to validate required columns.`);
  }
  if (data.length < 2) return [];

  var headers = data[0];
  var columnIndexes = {};

  // Validate that all required columns exist
  for (var i = 0; i < requiredColumns.length; i++) {
    var columnIndex = headers.indexOf(requiredColumns[i]);
    if (columnIndex === -1) {
      throw new Error(`Required column '${requiredColumns[i]}' not found in sheet '${sheet.getName()}'.`);
    }
    columnIndexes[requiredColumns[i]] = columnIndex;
  }

  // Validate that all required columns have values in each row
  var errors = [];
  for (var row = 1; row < data.length; row++) {
    for (var colName in columnIndexes) {
      var colIndex = columnIndexes[colName];
      if (!data[row][colIndex] || data[row][colIndex].toString().trim() === "") {
        errors.push(`Row ${row + 1}: '${colName}' is empty.`);
      }
    }
  }

  return errors;
}

/**
 * Validates UUID format in specified columns
 * @param {Sheet} sheet - The sheet to validate
 * @param {Array} columns - Array of column names to validate as UUIDs
 * @param {boolean} [warnOnly=false] - Whether to only warn instead of throwing errors
 * @return {Array} Array of validation errors (empty if none)
 */
function validateUUIDColumns(sheet, columns, warnOnly) {
  if (!columns || columns.length === 0 || !columns[0]) {
    return [];
  }

  var data = sheet.getDataRange().getValues();
  if (data.length < 2) {
    return [];
  }

  var headers = data[0];
  var uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  var errors = [];

  for (var k = 0; k < columns.length; k++) {
    var columnName = columns[k];
    if (!columnName) continue;

    var columnIndex = headers.indexOf(columnName);

    if (columnIndex === -1) {
      var errorMsg = {
        type: "column_not_found",
        column: columnName,
        sheet: sheet.getName()
      };

      if (warnOnly) {
        console.warn("UUID column '" + columnName + "' not found in sheet '" + sheet.getName() + "'.");
        errors.push(errorMsg);
      } else {
        throw new Error("UUID column '" + columnName + "' not found in sheet '" + sheet.getName() + "'.");
      }
      continue;
    }

    for (var i = 1; i < data.length; i++) {
      var value = data[i][columnIndex];
      if (value && value.toString().trim() !== "" && !uuidRegex.test(value)) {
        var errorMsg = {
          type: "invalid_uuid",
          column: columnName,
          row: i + 1,
          sheet: sheet.getName(),
          value: value
        };

        if (warnOnly) {
          console.warn("Invalid value in column '" + columnName + "' (row " + (i + 1) + " of sheet '" + sheet.getName() + "'): '" + value + "' is not a valid UUID.");
          errors.push(errorMsg);

          // Marca a célula com erro para visualização
          sheet.getRange(i + 1, columnIndex + 1).setBackground("#ffcccc");
        } else {
          throw new Error("Invalid value in column '" + columnName + "' (row " + (i + 1) + " of sheet '" + sheet.getName() + "'): '" + value + "' is not a valid UUID.");
        }
      }
    }
  }

  return errors;
}

/**
 * Validates that a sheet exists and has data
 * @param {string} sheetName - The name of the sheet to validate
 * @return {Sheet} The validated sheet
 */
function validateSheet(sheetName) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName(sheetName);

  if (!sheet) {
    throw new Error(`Sheet '${sheetName}' not found.`);
  }

  var data = sheet.getDataRange().getValues();
  if (data.length < 2) {
    throw new Error(`Sheet '${sheetName}' does not contain enough data (needs at least headers and one data row).`);
  }

  return sheet;
}

/**
 * Gets a column index by name
 * @param {Array} headers - Array of header names
 * @param {string} columnName - The column name to find
 * @param {boolean} required - Whether the column is required
 * @param {string} sheetName - The name of the sheet (for error messages)
 * @return {number} The column index or -1 if not found and not required
 */
function getColumnIndex(headers, columnName, required = false, sheetName = "") {
  var columnIndex = headers.indexOf(columnName);

  if (columnIndex === -1 && required) {
    throw new Error(`Required column '${columnName}' not found in sheet '${sheetName}'.`);
  }

  return columnIndex;
}
