function handleUpsert(sheetName) {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName(sheetName);

  if (!sheet) {
    throw new Error("A planilha '" + sheetName + "' não foi encontrada.");
  }

  const requiredColumns = {
    "priceListItems": ["priceList.code", "product.code", "salesPrice"],
    "customers": ["code", "name"], // Example: "code" and "name" might be required for customers
  };

  const externalIdColumns = {
    "priceListItems": ["priceList.code", "product.code"],
    "customers": ["code"],
  };

  // Coleta erros de API durante a busca de dados
  var apiErrors = [];

  // Inicializa variáveis para armazenar dados
  var priceListsData = { items: [] };
  var productsData = { items: [] };
  var customerPersonTypesData = { items: [] };
  var identificationDocumentsData = { items: [] };

  // Variáveis para armazenar respostas da API
  var priceListsResponse, productsResponse, customerPersonTypesResponse, identificationDocumentsResponse;

  // Captura erros de API diretamente apenas para os serviços necessários
  if (sheetName === 'priceListItems') {
    try {
      priceListsResponse = apiGet(
        "product",
        "price-lists-integration",
        "v1",
        "{code->in->[teste]}{select->[id,code]}{pageSize->1000}"
      );

      if (priceListsResponse.error) {
        apiErrors.push({
          column: "priceList.code",
          status: priceListsResponse.status || 500,
          error: priceListsResponse.error || "API Error",
          message: priceListsResponse.message || "Unknown error",
          response: priceListsResponse
        });
      }
    } catch (e) {
      console.error("Error fetching price lists: " + e.message);
      if (e.apiResponse) {
        apiErrors.push({
          column: "priceList.code",
          status: e.statusCode || 500,
          error: e.apiResponse.error || "API Error",
          message: e.apiResponse.message || e.message,
          response: e.apiResponse
        });
      }
    }

    try {
      productsResponse = apiGet(
        "product",
        "products-integration",
        "v2",
        "{code->in->[1064]}{select->[id,code]}{pageSize->1000}"
      );

      if (productsResponse.error) {
        apiErrors.push({
          column: "product.code",
          status: productsResponse.status || 500,
          error: productsResponse.error || "API Error",
          message: productsResponse.message || "Unknown error",
          response: productsResponse
        });
      }
    } catch (e) {
      console.error("Error fetching products: " + e.message);
      if (e.apiResponse) {
        apiErrors.push({
          column: "product.code",
          status: e.statusCode || 500,
          error: e.apiResponse.error || "API Error",
          message: e.apiResponse.message || e.message,
          response: e.apiResponse
        });
      }
    }
  }

  if (sheetName === 'customers') {
    try {
      customerPersonTypesResponse = apiGet(
        "customer",
        "customer-person-types-integration",
        "v1",
        "{code->in->[PJ]}{select->[id,code]}{pageSize->1000}"
      );

      if (customerPersonTypesResponse.error) {
        // Prepara a resposta para exibição
        var responseToShow = customerPersonTypesResponse;

        // Se a resposta for um objeto com propriedades, usa-o diretamente
        apiErrors.push({
          column: "customerPersonType.code",
          status: customerPersonTypesResponse.status || 500,
          error: customerPersonTypesResponse.error || "API Error",
          message: customerPersonTypesResponse.message || "Unknown error",
          response: responseToShow
        });
      }
    } catch (e) {
      console.error("Error fetching customer person types: " + e.message);
      if (e.apiResponse) {
        // Trata a resposta da API para exibição adequada
        var responseToShow = e.apiResponse;

        // Se a resposta for uma string JSON, tenta analisá-la
        if (typeof responseToShow === 'string' && responseToShow.startsWith('{')) {
          try {
            responseToShow = JSON.parse(responseToShow);
          } catch (parseError) {
            // Se falhar ao analisar, mantém a string original
            console.warn("Failed to parse API response as JSON: " + parseError.message);
          }
        }

        apiErrors.push({
          column: "customerPersonType.code",
          status: e.statusCode || 500,
          error: e.apiResponse.error || "API Error",
          message: e.apiResponse.message || e.message,
          response: responseToShow
        });
      }
    }

    // Busca documentos de identificação
    try {
      identificationDocumentsResponse = apiGet(
        "customer",
        "customer-identification-documents-integration",
        "v1",
        "{select->[id,identificationDocumentAcronym]}{pageSize->1000}"
      );

      if (identificationDocumentsResponse.error) {
        // Prepara a resposta para exibição
        var responseToShow = identificationDocumentsResponse;

        // Se a resposta for um objeto com propriedades, usa-o diretamente
        apiErrors.push({
          column: "customerIdentificationDocuments[0].identificationDocument.code",
          status: identificationDocumentsResponse.status || 500,
          error: identificationDocumentsResponse.error || "API Error",
          message: identificationDocumentsResponse.message || "Unknown error",
          response: responseToShow
        });
      }
    } catch (e) {
      console.error("Error fetching identification documents: " + e.message);
      if (e.apiResponse) {
        // Trata a resposta da API para exibição adequada
        var responseToShow = e.apiResponse;

        // Se a resposta for uma string JSON, tenta analisá-la
        if (typeof responseToShow === 'string' && responseToShow.startsWith('{')) {
          try {
            responseToShow = JSON.parse(responseToShow);
          } catch (parseError) {
            // Se falhar ao analisar, mantém a string original
            console.warn("Failed to parse API response as JSON: " + parseError.message);
          }
        }

        apiErrors.push({
          column: "customerIdentificationDocuments[0].identificationDocument.code",
          status: e.statusCode || 500,
          error: e.apiResponse.error || "API Error",
          message: e.apiResponse.message || e.message,
          response: responseToShow
        });
      }
    }
  }

  // Processa as respostas da API para obter os dados para substituição de códigos
  // Usa os dados da API se não houver erros
  if (sheetName === 'priceListItems') {
    if (!priceListsResponse || !priceListsResponse.error) {
      priceListsData = processApiResponse(priceListsResponse, item => item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined', "getPriceLists");
    }

    if (!productsResponse || !productsResponse.error) {
      productsData = processApiResponse(productsResponse, item => item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined', "getProducts");
    }
  }

  if (sheetName === 'customers') {
    if (!customerPersonTypesResponse || !customerPersonTypesResponse.error) {
      customerPersonTypesData = processApiResponse(customerPersonTypesResponse, item => item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined', "getCustomerPersonTypes");
    }

    if (!identificationDocumentsResponse || !identificationDocumentsResponse.error) {
      identificationDocumentsData = processApiResponse(identificationDocumentsResponse, item => item && typeof item.identificationDocumentAcronym !== 'undefined' && typeof item.id !== 'undefined', "getIdentificationDocuments");
    }
  }

  const foreignKeysColumns = {
    "priceListItems": [
      ["priceList.code", "priceList.id", priceListsData],
      ["product.code", "product.id", productsData],
    ],
    "customers": [
      ["customerPersonType.code", "customerPersonType.id", customerPersonTypesData],
      ["customerIdentificationDocuments[0].identificationDocumentAcronym", "customerIdentificationDocuments[0].identificationDocumentId", identificationDocumentsData],
      ["customerIdentificationDocuments[0].identificationDocument.code", "customerIdentificationDocuments[0].identificationDocumentId", identificationDocumentsData]
    ],
  };

  var missingValues = validateRequiredColumns(sheet, requiredColumns[sheetName] || []);
  if (missingValues.length > 0) {
    throw new Error("Validação falhou, corrija os seguintes erros:\n" + missingValues.join("\n"));
  }

  var today = new Date();
  var formattedDate = Utilities.formatDate(today, Session.getScriptTimeZone(), "yyyyMMdd");
  var newSheetName = sheetName + "_" + formattedDate;

  var existingSheet = ss.getSheetByName(newSheetName);
  if (existingSheet) {
    ss.deleteSheet(existingSheet);
  }

  var newSheet = sheet.copyTo(ss).setName(newSheetName);

  if (externalIdColumns[sheetName] && externalIdColumns[sheetName].length > 0) {
    addExternalIdColumn(newSheet, externalIdColumns[sheetName]);
  }

  // Adiciona o campo customerIdentificationDocuments[0].externalId para a planilha customers
  if (sheetName === 'customers') {
    addCustomerIdentificationDocumentsExternalId(newSheet);
  }

  // Adiciona colunas para rastrear erros de API
  var lastColumnIndex = newSheet.getLastColumn();
  newSheet.getRange(1, lastColumnIndex + 1).setValue('HTTP Status');
  newSheet.getRange(1, lastColumnIndex + 2).setValue('Response Body');
  var resultColumnIndex = lastColumnIndex + 1;

  // Se houver erros de API, exibe-os e pergunta se deseja continuar
  if (apiErrors.length > 0) {
    // Prepara os detalhes dos erros para exibição
    var errorDetails = {
      sheetName: sheetName,
      apiErrors: apiErrors
    };

    // Mostra o erro e pergunta se deseja continuar
    showErrorDialog(
      "Erros na API",
      "Foram detectados erros ao acessar a API para a planilha '" + sheetName + "'. Isso pode causar problemas na validação e no upsert.",
      errorDetails
    );

    var ui = SpreadsheetApp.getUi();
    var response = ui.alert(
      "Continuar mesmo com erros?",
      "Foram detectados erros na API. Deseja continuar mesmo assim?\n\n" +
      "Aviso: Continuar pode resultar em erros de validação de UUID.",
      ui.ButtonSet.YES_NO
    );

    if (response !== ui.Button.YES) {
      return; // Encerra a execução se o usuário não quiser continuar
    }

    // Se o usuário optar por continuar, marca que houve erros de API
    // para que a validação de UUID seja mais tolerante
    var continueWithApiErrors = true;
  }

  // Se chegou aqui, não há erros de API, então prossegue com a substituição de códigos
  try {
    (foreignKeysColumns[sheetName] || []).forEach(foreignKey => {
      if (foreignKey && foreignKey.length === 3) {
        replaceCode(newSheet, foreignKey[0], foreignKey[1], foreignKey[2]);
      }
    });
  } catch (e) {
    console.error("Error during code replacement: " + e.message);

    // Mostra o erro e encerra
    showErrorDialog(
      "Erro ao substituir códigos",
      e,
      { sheetName: sheetName }
    );

    return; // Encerra a execução imediatamente
  }

  // Valida UUIDs com tratamento de erros
  try {
    // Valida cada coluna de UUID
    for (var i = 0; i < foreignKeysColumns[sheetName].length; i++) {
      var foreignKey = foreignKeysColumns[sheetName][i];
      if (foreignKey && foreignKey.length > 1 && foreignKey[1]) {
        try {
          // Sempre usa validação tolerante para documentos de identificação
          if (foreignKey[1] === "customerIdentificationDocuments[0].identificationDocumentId" ||
              typeof continueWithApiErrors !== 'undefined' && continueWithApiErrors) {
            var errors = validateUUIDColumns(newSheet, [foreignKey[1]], true, true);
            if (errors && errors.length > 0) {
              console.warn("UUID validation warnings (continuing anyway): " + JSON.stringify(errors));
            }
          } else {
            // Usa validação estrita (não warnOnly) para capturar erros imediatamente
            validateUUIDColumns(newSheet, [foreignKey[1]]);
          }
        } catch (validationError) {
          // Se houver erro de validação, mostra e encerra
          console.error("UUID validation error: " + validationError.message);

          // Sempre inclui os erros de API nos detalhes do erro de validação
          showErrorDialog(
            "Erro de validação de UUID",
            validationError,
            {
              column: foreignKey[1],
              sheetName: sheetName,
              apiErrors: apiErrors // Sempre inclui os erros de API
            }
          );

          return; // Encerra a execução imediatamente
        }
      }
    }
  } catch (e) {
    console.error("Error during UUID validation: " + e.message);

    // Sempre inclui os erros de API nos detalhes do erro
    showErrorDialog(
      "Erro durante validação",
      e,
      {
        sheetName: sheetName,
        apiErrors: apiErrors // Sempre inclui os erros de API
      }
    );

    return; // Encerra a execução imediatamente
  }

  if (sheetName === "priceListItems") {
    prepareUpdate(newSheet);
  } else if (sheetName === "customers") {
    prepareCustomerUpdate(newSheet);
  }

  var rowDataObjectsArray = prepareJsonBodyFromSheet(newSheet);

  let integrationVersion, integrationService, integrationAction, integrationResource;

  // Obtém o token de autenticação, com tratamento de erro
  let authToken;
  try {
    authToken = getConfiguration("auth_token");
  } catch (e) {
    console.error("Erro ao obter token de autenticação: " + e.message);
    throw new Error("Token de autenticação não configurado. Configure o token em 'configuration' com a chave 'auth_token'.");
  }

  // Obtém a URL de envio, com tratamento de erro
  let currentUrlToSend;
  try {
    currentUrlToSend = urlToSend; // Usa a variável global definida no final do arquivo
    if (!currentUrlToSend) {
      currentUrlToSend = getGatewaySendUrl(); // Tenta obter a URL do gateway
    }
  } catch (e) {
    console.warn("Erro ao obter URL de envio: " + e.message + ". Usando URL padrão.");
    currentUrlToSend = "https://csvbox-gateway.satellite.totvscrm.app/";
  }

  if (sheetName === 'priceListItems') {
    integrationAction = "upsert";
    integrationService = "product";
    integrationResource = "price-list-items-integration";
    integrationVersion = "v1";
  } else if (sheetName === 'customers') {
    integrationAction = "upsert";
    integrationService = "customer";
    integrationResource = "customers-integration";
    integrationVersion = "v4";
  }

  if (!integrationVersion || !integrationService || !integrationAction || !integrationResource) {
    throw new Error("Não foi possível determinar as configurações de integração (Version, Service, Action, Resource) para: " + sheetName);
  }

  // Neste ponto, todos os erros já foram verificados e tratados
  // Se chegou aqui, não há erros que impeçam a continuação

  // Contador de sucessos e erros
  var successCount = 0;
  var errorCount = 0;

  // Processa cada linha de dados
  rowDataObjectsArray.forEach(rowDataObject => {
    const completeJsonPayload = JSON.stringify([
      {
        "version": "2.0",
        "routeKey": "$default",
        "rawPath": "/my/path",
        "rawQueryString": "",
        "cookies": [],
        "headers": {
            "content-type": "application/json",
            "integration-version": integrationVersion,
            "integration-service": integrationService,
            "integration-action": integrationAction,
            "integration-resource": integrationResource,
            "Authorization": `Bearer ${authToken}`
        },
        "queryStringParameters": {},
        "requestContext": {},
        "row_data": rowDataObject.row_data,
        "pathParameters": null,
        "isBase64Encoded": false,
        "stageVariables": null
      }
    ]);

    try {
      var result = sendJsonToUrl(completeJsonPayload, newSheet, rowDataObject.row_number, resultColumnIndex, currentUrlToSend);

      // Conta sucessos e erros
      if (result && result.statusCode && result.statusCode >= 200 && result.statusCode < 300) {
        successCount++;
      } else {
        errorCount++;
      }
    } catch (e) {
      console.error("Error sending data for row " + rowDataObject.row_number + ": " + e.message);
      errorCount++;
    }
  });

  // Exibe um resumo da operação para o usuário
  var ui = SpreadsheetApp.getUi();
  ui.alert(
    "Operação concluída",
    `Operação de upsert para ${sheetName} concluída.\n\n` +
    `Registros processados: ${rowDataObjectsArray.length}\n` +
    `Sucessos: ${successCount}\n` +
    `Erros: ${errorCount}\n\n` +
    `Os resultados detalhados estão disponíveis na planilha ${newSheet.getName()}.`,
    ui.ButtonSet.OK
  );
}

function replaceCode(sheet,columnName,newColumnName,referenceObject) {
  var data = sheet.getDataRange().getValues();

  if (data.length < 2) {
    return;
  }

  var headers = data[0];
  var columnIndex = headers.indexOf(columnName);

  if (columnIndex === -1) {
    console.warn("replaceCode: Cabeçalho '" + columnName + "' não encontrado na planilha '" + sheet.getName() + "'.");
    return;
  }

  var codeToIdMap = {};
  referenceObject.items.forEach(item => {
    // Verifica se estamos lidando com documentos de identificação
    if ((columnName === "customerIdentificationDocuments[0].identificationDocument.code" ||
         columnName === "customerIdentificationDocuments[0].identificationDocumentAcronym") &&
        typeof item.identificationDocumentAcronym !== 'undefined' &&
        typeof item.id !== 'undefined') {
      // Usa o ID do item diretamente, não o identificationDocumentId
      codeToIdMap[item.identificationDocumentAcronym] = item.id;
      console.log(`Mapeando acrônimo '${item.identificationDocumentAcronym}' para ID '${item.id}'`);
    }
    // Caso contrário, usa o campo code e id padrão
    else if (typeof item.code !== 'undefined' && typeof item.id !== 'undefined') {
      codeToIdMap[item.code] = item.id;
    }
  });

  if (Object.keys(codeToIdMap).length === 0 && referenceObject.items.length > 0) {
    console.warn("replaceCode: Nenhum mapeamento de código para ID foi efetivamente criado para a coluna '" + columnName + "' a partir de referenceObject, embora referenceObject.items não estivesse vazio. Verifique a estrutura dos itens. referenceObject.items: " + JSON.stringify(referenceObject.items));
  } else if (referenceObject.items.length === 0) {
     console.warn("replaceCode: Nenhum item retornado pela API para mapeamento da coluna '" + columnName + "'.");
  }


  for (var i = 1; i < data.length; i++) {
    var code = data[i][columnIndex];

    // Adiciona log para verificar o valor do código
    if (columnName === "customerIdentificationDocuments[0].identificationDocument.code" ||
        columnName === "customerIdentificationDocuments[0].identificationDocumentAcronym") {
      console.log(`Verificando código '${code}' na linha ${i+1} para coluna '${columnName}'`);
      console.log(`Mapeamento disponível: ${JSON.stringify(Object.keys(codeToIdMap))}`);
    }

    if (codeToIdMap[code]) {
      console.log(`Substituindo '${code}' por '${codeToIdMap[code]}' na linha ${i+1}, coluna '${columnName}'`);
      sheet.getRange(i + 1, columnIndex + 1).setValue(codeToIdMap[code]);
    } else if (code && code.toString().trim() !== "") {
      console.warn("replaceCode: Código '" + code + "' na planilha (coluna '" + columnName + "', linha " + (i+1) + ") não encontrado no mapeamento da API.");
    }
  }

  for (var i = 0; i < headers.length; i++) {
    if (headers[i] == columnName) {
      sheet.getRange(1, i + 1).setValue(newColumnName);
    }
  }
}

function addExternalIdColumn(sheet, fieldsArray) {
  var data = sheet.getDataRange().getValues();
  if (data.length < 2) {
    console.warn("addExternalIdColumn: A planilha clonada '" + sheet.getName() + "' não tem dados suficientes para adicionar externalId.");
    return;
  }

  var headers = data[0];
  var fieldIndexes = {};

  for (var i = 0; i < fieldsArray.length; i++) {
    var fieldIndex = headers.indexOf(fieldsArray[i]);
    if (fieldIndex === -1) {
      throw new Error(`addExternalIdColumn: A coluna '${fieldsArray[i]}' necessária para externalId não foi encontrada.`);
    }
    fieldIndexes[fieldsArray[i]] = fieldIndex;
  }

  var importationIdentifier = getConfiguration("importation_identifier");
  if (!importationIdentifier) {
      console.warn("addExternalIdColumn: 'importation_identifier' não encontrado na configuração. O externalId pode ficar incompleto.");
      importationIdentifier = "DEFAULT_IMPORT_ID";
  }
  headers.push("externalId");

  for (var i = 1; i < data.length; i++) {
    var concatenatedValues = fieldsArray.map(field => data[i][fieldIndexes[field]] || "").join("|");
    data[i].push(`${importationIdentifier}|${concatenatedValues}`);
  }
  sheet.getRange(1, 1, data.length, data[0].length).setValues(data);
}

/**
 * Adiciona o campo customerIdentificationDocuments[0].externalId na planilha de clientes
 * O valor será gerado no formato importation_identifier#code#0
 * @param {Sheet} sheet - A planilha de clientes
 */
function addCustomerIdentificationDocumentsExternalId(sheet) {
  var data = sheet.getDataRange().getValues();
  if (data.length < 2) {
    console.warn("addCustomerIdentificationDocumentsExternalId: A planilha clonada '" + sheet.getName() + "' não tem dados suficientes.");
    return;
  }

  var headers = data[0];

  // Verifica se a coluna code existe
  var codeIndex = headers.indexOf("code");
  if (codeIndex === -1) {
    console.warn("addCustomerIdentificationDocumentsExternalId: A coluna 'code' não foi encontrada. Não é possível adicionar o campo customerIdentificationDocuments[0].externalId.");
    return;
  }

  // Verifica se a coluna customerIdentificationDocuments[0].documentNumber existe
  var documentNumberIndex = headers.indexOf("customerIdentificationDocuments[0].documentNumber");
  if (documentNumberIndex === -1) {
    console.warn("addCustomerIdentificationDocumentsExternalId: A coluna 'customerIdentificationDocuments[0].documentNumber' não foi encontrada. Não é possível adicionar o campo customerIdentificationDocuments[0].externalId.");
    return;
  }

  // Verifica se a coluna customerIdentificationDocuments[0].externalId já existe
  var targetColumnIndex = headers.indexOf("customerIdentificationDocuments[0].externalId");

  // Se a coluna já existe, não faz nada
  if (targetColumnIndex !== -1) {
    console.log("addCustomerIdentificationDocumentsExternalId: A coluna 'customerIdentificationDocuments[0].externalId' já existe.");
    return;
  }

  // Obtém o importation_identifier da configuração
  var importationIdentifier = getConfiguration("importation_identifier");
  if (!importationIdentifier) {
    console.warn("addCustomerIdentificationDocumentsExternalId: 'importation_identifier' não encontrado na configuração. Usando valor padrão.");
    importationIdentifier = "DEFAULT_IMPORT_ID";
  }

  // Adiciona a nova coluna no final
  headers.push("customerIdentificationDocuments[0].externalId");

  // Preenche os valores para cada linha
  for (var i = 1; i < data.length; i++) {
    var code = data[i][codeIndex] || "";
    var documentNumber = data[i][documentNumberIndex];

    // Adiciona o externalId apenas se documentNumber estiver preenchido
    if (documentNumber && documentNumber.toString().trim() !== "") {
      // Formato: importation_identifier#code#0
      data[i].push(`${importationIdentifier}#${code}#0`);
    } else {
      // Se documentNumber não estiver preenchido, adiciona uma string vazia
      data[i].push("");
    }
  }

  // Atualiza a planilha com os novos dados
  sheet.getRange(1, 1, data.length, data[0].length).setValues(data);

  console.log("addCustomerIdentificationDocumentsExternalId: Campo 'customerIdentificationDocuments[0].externalId' adicionado com sucesso.");
}

function validateRequiredColumns(sheet, requiredColumns) {
  if (!requiredColumns || requiredColumns.length === 0) {
    return [];
  }
  var data = sheet.getDataRange().getValues();
  if (data.length < 2 && requiredColumns.length > 0) {
    throw new Error("A planilha clonada não tem dados suficientes para validar colunas obrigatórias.");
  }
  if (data.length < 2) return [];


  var headers = data[0];
  var columnIndexes = {};

  for (var i = 0; i < requiredColumns.length; i++) {
    var columnIndex = headers.indexOf(requiredColumns[i]);
    if (columnIndex === -1) {
      throw new Error(`A coluna obrigatória '${requiredColumns[i]}' não foi encontrada.`);
    }
    columnIndexes[requiredColumns[i]] = columnIndex;
  }

  var errors = [];
  for (var row = 1; row < data.length; row++) {
    for (var colName in columnIndexes) {
      var colIndex = columnIndexes[colName];
      if (!data[row][colIndex] || data[row][colIndex].toString().trim() === "") {
        errors.push(`Linha ${row + 1}: '${colName}' está vazio.`);
      }
    }
  }
  return errors;
}

function validateUUIDColumns(sheet, columns) {
  if (!columns || columns.length === 0 || !columns[0]) {
      return;
  }
  var data = sheet.getDataRange().getValues();
  if (data.length < 2) {
      return;
  }

  var headers = data[0];
  var uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  for (var k = 0; k < columns.length; k++) {
    var columnName = columns[k];
    if (!columnName) continue;

    var columnIndex = headers.indexOf(columnName);

    if (columnIndex === -1) {
      throw new Error(`A coluna de UUID especificada '${columnName}' não foi encontrada na planilha '${sheet.getName()}'.`);
    }

    for (var i = 1; i < data.length; i++) {
      var value = data[i][columnIndex];
      if (value && value.toString().trim() !== "" && !uuidRegex.test(value)) {
        throw new Error(`Valor inválido na coluna '${columnName}' (linha ${i + 1} da planilha): '${value}' não é um UUID válido.`);
      }
    }
  }
}

/**
 * Envia dados JSON para uma URL
 * @param {string} completeJsonPayload - O payload JSON completo a ser enviado
 * @param {Sheet} sheet - A planilha onde os resultados serão registrados
 * @param {number} row - O número da linha na planilha
 * @param {number} resultColumnIndex - O índice da coluna onde os resultados serão registrados
 * @param {string} [url] - A URL para onde os dados serão enviados (opcional)
 * @return {Object} Objeto com o resultado da operação (statusCode, body)
 */
function sendJsonToUrl(completeJsonPayload, sheet, row, resultColumnIndex, url) {
  // Se a URL não for fornecida, usa a URL padrão do gateway
  if (!url) {
      console.warn("sendJsonToUrl: URL não fornecida para a linha " + row + ". Usando URL padrão do gateway.");
      url = getGatewaySendUrl();

      // Se ainda não tiver URL, registra o erro e retorna
      if (!url) {
          console.error("sendJsonToUrl: Não foi possível obter URL do gateway para a linha " + row);
          sheet.getRange(row, resultColumnIndex).setValue("Error");
          sheet.getRange(row, resultColumnIndex + 1).setValue("URL de destino não configurada e não foi possível obter URL padrão.");
          return {
            statusCode: 500,
            body: "URL de destino não configurada e não foi possível obter URL padrão."
          };
      }
  }

  const payloadObject = JSON.parse(completeJsonPayload);
  const gatewayHeaders = payloadObject[0].headers;

  const options = {
    "method": "post",
    "headers": gatewayHeaders,
    "payload": completeJsonPayload,
    "muteHttpExceptions": true
  };

  console.log("sendJsonToUrl - Enviando para URL: " + url + " Linha: " + row);
  console.log("sendJsonToUrl - Opções: " + JSON.stringify(options));

  try {
    const response = UrlFetchApp.fetch(url, options);
    const statusCode = response.getResponseCode();
    const responseBody = response.getContentText();

    console.log("sendJsonToUrl - Resposta para Linha " + row + ": Status " + statusCode + ", Body: " + responseBody.substring(0,500));

    // Registra o resultado na planilha
    sheet.getRange(row, resultColumnIndex).setValue(statusCode);
    sheet.getRange(row, resultColumnIndex + 1).setValue(responseBody);

    // Formata a célula de acordo com o status
    if (statusCode >= 200 && statusCode < 300) {
      sheet.getRange(row, resultColumnIndex).setBackground("#d9ead3"); // Verde claro para sucesso
    } else {
      sheet.getRange(row, resultColumnIndex).setBackground("#f4cccc"); // Vermelho claro para erro

      // Extrai a mensagem de erro e a adiciona diretamente na coluna Response Body
      try {
        var parsedResponse = JSON.parse(responseBody);
        if (parsedResponse.message) {
          // Adiciona a mensagem de erro formatada na mesma coluna Response Body
          var formattedResponse = JSON.stringify(parsedResponse, null, 2);
          sheet.getRange(row, resultColumnIndex + 1).setValue(formattedResponse);
        }
      } catch (parseError) {
        // Ignora erros de análise JSON
      }
    }

    return {
      statusCode: statusCode,
      body: responseBody
    };
  } catch (e) {
    console.error("Error during UrlFetchApp.fetch for row " + row + ": " + e.message + ". Payload: " + completeJsonPayload.substring(0,500));

    // Registra o erro na planilha
    sheet.getRange(row, resultColumnIndex).setValue("Error Fetch");
    sheet.getRange(row, resultColumnIndex).setBackground("#f4cccc"); // Vermelho claro para erro
    sheet.getRange(row, resultColumnIndex + 1).setValue(e.message);

    return {
      statusCode: 500,
      body: e.message
    };
  }
}

/**
 * Prepara a atualização de clientes adicionando o campo integrationFilterId
 * @param {Sheet} sheet - A planilha de clientes
 */
function prepareCustomerUpdate(sheet) {
    var apiData = getCustomers(sheet);
    var data = sheet.getDataRange().getValues();

    if (data.length < 2) {
        return;
    }

    var headers = data[0];

    var codeIndex = headers.indexOf("code");
    var updateIdIndex = headers.indexOf("integrationFilterId");

    if (codeIndex === -1) {
        console.warn("prepareCustomerUpdate: Column 'code' not found in sheet. Cannot prepare update.");
        return;
    }

    if (updateIdIndex === -1) {
        // Adiciona a coluna integrationFilterId no início (à esquerda)
        sheet.insertColumnBefore(1);
        sheet.getRange(1, 1).setValue("integrationFilterId");
        updateIdIndex = 0;

        // Atualiza os índices das colunas após a inserção
        codeIndex++;
    }

    var apiMap = new Map();
    if (apiData && Array.isArray(apiData.items)) {
        apiData.items.forEach(item => {
            if (item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined') {
                apiMap.set(item.code, item.id);
            }
        });
    } else {
        console.warn("prepareCustomerUpdate: apiData.items não é um array válido. apiData: " + JSON.stringify(apiData));
    }

    for (var i = 1; i < data.length; i++) {
        var code = data[i][codeIndex];

        if (code) {
            if (apiMap.has(code)) {
                // Como updateIdIndex agora é 0, usamos a coluna 1 (primeira coluna)
                sheet.getRange(i + 1, updateIdIndex + 1).setValue(apiMap.get(code));
                console.log("prepareCustomerUpdate: Added integrationFilterId for customer code " + code + ": " + apiMap.get(code));
            } else {
                console.log("prepareCustomerUpdate: No existing customer found for code " + code);
            }
        }
    }
}

function prepareJsonBodyFromSheet(sheet) {
  var data = sheet.getDataRange().getValues();

  if (data.length < 2) {
    return [];
  }

  var headers = data[0];
  var rowDataArray = [];

  for (var i = 1; i < data.length; i++) {
    var rowObject = {};
    for (var j = 0; j < headers.length; j++) {
      if (headers[j]) {
        rowObject[headers[j]] = data[i][j];
      }
    }
    rowDataArray.push({
      "import_id": null,
      "sheet_id": null,
      "sheet_name": sheet.getName(),
      "row_number": i + 1,
      "total_rows": data.length - 1,
      "custom_fields": {},
      "row_data": rowObject
    });
  }
  return rowDataArray;
}

/**
 * Obtém a URL do gateway para envio de dados.
 * Se a configuração não estiver definida, usa um valor padrão.
 * @return {string} A URL do gateway
 */
function getGatewaySendUrl() {
  try {
    // Tenta obter a URL do gateway das configurações
    return getConfiguration("gateway_send_url");
  } catch (e) {
    // Se a configuração não for encontrada, usa o valor padrão
    console.warn("Configuração 'gateway_send_url' não encontrada. Usando valor padrão.");
    return "https://csvbox-gateway.satellite.totvscrm.app/";
  }
}

// Define a URL para envio de dados
const urlToSend = getGatewaySendUrl();