/**
 * Functions for handling customers
 */

/**
 * Gets customers from the API
 * @param {Sheet} sheet - The sheet to get customer codes from
 * @return {Object} Object with items array containing customers
 */
function getCustomers(sheet) {
    if (!sheet) {
        console.warn("Sheet not provided", { function: "getCustomers" });
        return { items: [] };
    }

    var customerCodes = getCustomerCodes(sheet);

    if (!customerCodes) {
        console.warn("No customer codes found in sheet to fetch", { sheetName: sheet.getName() });
        return { items: [] };
    }

    try {
        const integrationFilter = `{code->in->[${customerCodes}]}{select->[id,code]}{pageSize->1000}`;
        const parsedResponse = apiGet(
            "customer",
            "customers-integration",
            "v4",
            integrationFilter
        );

        return processApiResponse(
            parsedResponse,
            item => item && typeof item.code !== 'undefined' && typeof item.id !== 'undefined',
            "getCustomers"
        );
    } catch (e) {
        console.error("Error fetching customers", e, { customerCodes });
        throw e; // Propagate error with new structure
    }
}

/**
 * Gets customer codes from a sheet
 * @param {Sheet} sheet - The sheet to get customer codes from
 * @return {string} Comma-separated list of customer codes
 */
function getCustomerCodes(sheet) {
    if (!sheet) {
        console.warn("Sheet not provided", { function: "getCustomerCodes" });
        return "";
    }

    var data = sheet.getDataRange().getValues();

    if (data.length < 2) {
        console.warn("Sheet has no data rows", { sheetName: sheet.getName() });
        return "";
    }

    var headers = data[0];
    var columnIndex = headers.indexOf("code");

    if (columnIndex === -1) {
        console.warn("Column 'code' not found in sheet", { sheetName: sheet.getName() });
        return "";
    }

    var uniqueCodes = new Set();
    for (var i = 1; i < data.length; i++) {
        var code = data[i][columnIndex];
        if (code && code.toString().trim() !== "") {
            uniqueCodes.add(code.toString().trim());
        }
    }

    var codesArray = Array.from(uniqueCodes);
    console.log("Extracted customer codes", {
        count: codesArray.length,
        codes: codesArray.join(","),
        sheetName: sheet.getName()
    });

    return codesArray.join(",");
}
