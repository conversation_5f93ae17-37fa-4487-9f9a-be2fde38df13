/**
 * Centralized logging module for consistent logging across the application
 */

// Define log level values as variables instead of constants
var LOG_LEVEL_DEBUG = 0;
var LOG_LEVEL_INFO = 1;
var LOG_LEVEL_WARN = 2;
var LOG_LEVEL_ERROR = 3;

// Current log level (can be changed at runtime)
var _currentLogLevel = 1; // Default to INFO level

/**
 * Sets the current log level
 * @param {number} level - The log level to set
 */
function setLogLevel(level) {
  if (level >= 0 && level <= 3) {
    _currentLogLevel = level;
  } else {
    console.error("Invalid log level: " + level);
  }
}

/**
 * Gets the current log level
 * @return {number} The current log level
 */
function getLogLevel() {
  return _currentLogLevel;
}

/**
 * Formats a log message with context information
 * @param {string} level - The log level
 * @param {string} message - The log message
 * @param {Object} context - Additional context information
 * @return {string} The formatted log message
 */
function formatLogMessage(level, message, context) {
  var timestamp = new Date().toISOString();
  var formattedMessage = "[" + timestamp + "] [" + level + "] " + message;

  if (context) {
    try {
      formattedMessage += " - Context: " + JSON.stringify(context);
    } catch (e) {
      formattedMessage += " - Context: [Object cannot be stringified]";
    }
  }

  return formattedMessage;
}

/**
 * Logs a debug message
 * @param {string} message - The message to log
 * @param {Object} context - Additional context information
 */
function logDebug(message, context) {
  if (_currentLogLevel <= LOG_LEVEL_DEBUG) {
    console.log(formatLogMessage('DEBUG', message, context));
  }
}

/**
 * Logs an info message
 * @param {string} message - The message to log
 * @param {Object} context - Additional context information
 */
function logInfo(message, context) {
  if (_currentLogLevel <= LOG_LEVEL_INFO) {
    console.log(formatLogMessage('INFO', message, context));
  }
}

/**
 * Logs a warning message
 * @param {string} message - The message to log
 * @param {Object} context - Additional context information
 */
function logWarn(message, context) {
  if (_currentLogLevel <= LOG_LEVEL_WARN) {
    console.warn(formatLogMessage('WARN', message, context));
  }
}

/**
 * Logs an error message
 * @param {string} message - The message to log
 * @param {Object} context - Additional context information
 */
function logError(message, context) {
  if (_currentLogLevel <= LOG_LEVEL_ERROR) {
    console.error(formatLogMessage('ERROR', message, context));
  }
}

/**
 * Logs an exception with stack trace
 * @param {string} message - The message to log
 * @param {Error} error - The error object
 * @param {Object} context - Additional context information
 */
function logException(message, error, context) {
  if (_currentLogLevel <= LOG_LEVEL_ERROR) {
    var errorContext = context || {};
    errorContext.errorMessage = error.message;
    errorContext.stack = error.stack;

    console.error(formatLogMessage('EXCEPTION', message, errorContext));
  }
}
