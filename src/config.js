/**
 * Configuration module for managing application settings
 */

// Create a script property to store the configuration cache
// This approach avoids global variable issues in Google Apps Script
var _configCache = null;

/**
 * Initializes the configuration cache
 * @return {Object} The configuration cache object
 */
function initConfigCache() {
  if (_configCache === null) {
    _configCache = {};
  }
  return _configCache;
}

/**
 * Gets a configuration value from the configuration sheet
 * @param {string} key - The configuration key to retrieve
 * @param {boolean} useCache - Whether to use cached values (default: true)
 * @return {string} The configuration value
 */
function getConfiguration(key, useCache = true) {
  // Initialize cache if needed
  var cache = initConfigCache();

  // Return cached value if available and cache is enabled
  if (useCache && cache[key] !== undefined) {
    return cache[key];
  }

  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName("configuration");
  if (!sheet) {
    throw new Error("Configuration sheet 'configuration' not found!");
  }

  var data = sheet.getDataRange().getValues();

  for (var i = 0; i < data.length; i++) {
    if (data[i][0] === key) {
      // Cache the value for future use
      cache[key] = data[i][1];
      return data[i][1];
    }
  }

  // Check for default values
  const defaultValue = getDefaultConfiguration(key);
  if (defaultValue !== null) {
    console.log("Using default value for configuration " + key + ": " + defaultValue);
    cache[key] = defaultValue;
    return defaultValue;
  }

  // Throw a specific error message that can be caught and handled
  throw new Error("Configuration " + key + " not found!");
}

/**
 * Gets default configuration values for known keys
 * @param {string} key - The configuration key
 * @return {string|null} The default value or null if no default exists
 */
function getDefaultConfiguration(key) {
  // Define default values for common configuration keys
  var defaults = {
    "api_base_url": "https://totvscrm.app/api/v1/integration/record",
    "gateway_send_url": "https://csvbox-gateway.satellite.totvscrm.app/",
    "importation_identifier": "DEFAULT_IMPORT_ID",
    "upsert_importation_url": "https://totvscrm.app/importation/upsert",
    "delete_importation_url": "https://totvscrm.app/importation/delete",
    "auth_token": "" // Empty default, should be set by user
  };

  // Check if the key exists in defaults and return the value, otherwise return null
  return defaults[key] !== undefined ? defaults[key] : null;
}

/**
 * Clears the configuration cache
 */
function clearConfigCache() {
  _configCache = {};
}

/**
 * Sets a configuration value in the configuration sheet
 * @param {string} key - The configuration key to set
 * @param {string} value - The value to set
 * @return {boolean} True if successful, false otherwise
 */
function setConfiguration(key, value) {
  // Initialize cache if needed
  var cache = initConfigCache();

  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var sheet = ss.getSheetByName("configuration");
  if (!sheet) {
    console.error("Configuration sheet 'configuration' not found!");
    return false;
  }

  var data = sheet.getDataRange().getValues();

  // Check if key exists and update
  for (var i = 0; i < data.length; i++) {
    if (data[i][0] === key) {
      sheet.getRange(i + 1, 2).setValue(value);
      // Update cache
      cache[key] = value;
      return true;
    }
  }

  // Key doesn't exist, add it
  sheet.appendRow([key, value]);
  // Update cache
  cache[key] = value;
  return true;
}
