function onOpen() {
  var ui = SpreadsheetApp.getUi();
  ui.createMenu('Opções personalizadas')
    .addItem("[Upsert] priceListItems", "handlePriceListItemsUpsert")
    .addItem("[Upsert] customers", "handleCustomersUpsert")
    .addSeparator()
    .addItem("Limpar planilhas geradas", "cleanGeneratedSheets")
    .addToUi();
}

function handleUpsert(entityName) {
  TemplateScriptsCSVBOX.handleUpsert(entityName);
}

function handlePriceListItemsUpsert() {
  handleUpsert('priceListItems');
}

function handleCustomersUpsert() {
  handleUpsert('customers');
}

/**
 * <PERSON><PERSON> as planilhas geradas durante as operações de upsert
 * Identifica planilhas com nomes como "customers_YYYYMMDD" e "priceListItems_YYYYMMDD"
 */
function cleanGeneratedSheets() {
  TemplateScriptsCSVBOX.cleanGeneratedSheets();
}