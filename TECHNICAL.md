# Technical Documentation for CSV Integration Sheet

This document provides detailed technical information about the CSV Integration Sheet project for developers and AI assistants working with the codebase.

## Project Structure

```
/
├── src/
│   ├── api.js            # API client for TOTVS CRM
│   ├── config.js         # Configuration management
│   ├── customerPersonTypes.js # Customer person types integration
│   ├── export.js         # Data export and upsert operations
│   ├── logger.js         # Logging system
│   ├── main.js           # Application entry point
│   ├── priceLists.js     # Price lists integration
│   ├── prepareUpdate.js  # Data preparation for updates
│   ├── products.js       # Products integration
│   ├── utils.js          # UI and utility functions
│   └── validation.js     # Data validation utilities
├── appsscript.json       # Project metadata
└── README.md             # Project documentation
```

## Module Dependencies

The project has the following module dependencies:

```
main.js
 ├── config.js
 ├── logger.js
 ├── utils.js
 └── api.js

utils.js
 ├── config.js
 └── export.js

export.js
 ├── api.js
 ├── config.js
 ├── products.js
 ├── priceLists.js
 ├── customerPersonTypes.js
 └── prepareUpdate.js

products.js, priceLists.js, customerPersonTypes.js
 ├── api.js
 ├── config.js
 ├── logger.js
 └── validation.js

prepareUpdate.js
 └── priceLists.js
```

## Google Apps Script Constraints

This project is built as a Google Apps Script application, which imposes several constraints:

1. **Global Scope**: All functions are in the global scope, as Google Apps Script doesn't support ES modules.
2. **Limited ES6+ Support**: Some modern JavaScript features may not be fully supported.
3. **Execution Time Limits**: Scripts have a maximum execution time of 6 minutes.
4. **Service Quotas**: There are quotas for API calls, document operations, etc.
5. **No Local Storage**: Data must be stored in Google services (Sheets, Properties, Cache).

## API Integration Details

### TOTVS CRM API

The application integrates with the TOTVS CRM API with the following characteristics:

- **Base URL**: `https://totvscrm.app/api/v1/integration/record`
- **Authentication**: Bearer token authentication
- **Integration Headers**:
  - `Integration-Service`: Service identifier (e.g., "product", "customer")
  - `Integration-Resource`: Resource identifier (e.g., "products-integration")
  - `Integration-Version`: API version (e.g., "v1", "v2")
  - `Integration-Filter`: Query filter in a custom format

### Filter Format

The API uses a custom filter format:
```
{code->in->[value1,value2]}{select->[id,code]}{pageSize->1000}
```

Components:
- `{field->operator->[values]}`: Condition clause
- `{select->[fields]}`: Field selection
- `{pageSize->limit}`: Pagination control

## Data Flow

1. **Data Retrieval**:
   - User triggers a data retrieval operation
   - Application fetches codes from the sheet
   - API call is made to retrieve data
   - Response is processed and returned

2. **Code Replacement**:
   - User triggers a code replacement operation
   - Application fetches reference data from the API
   - Codes in the sheet are replaced with IDs
   - New sheet is created with replaced values

3. **Upsert Operation**:
   - User triggers an upsert operation
   - Application prepares data from the sheet
   - Data is sent to the API for upsert
   - Results are recorded in the sheet

## Configuration System

The configuration system uses a sheet named "configuration" with key-value pairs:

| Key | Value |
|-----|-------|
| auth_token | Bearer token for API authentication |
| api_base_url | Base URL for the API |
| gateway_send_url | URL for the gateway |
| upsert_importation_url | URL for upsert importation |
| delete_importation_url | URL for delete importation |
| importation_identifier | Identifier for importation |

The configuration is cached in memory for performance:

```javascript
var _configCache = null;

function initConfigCache() {
  if (_configCache === null) {
    _configCache = {};
  }
  return _configCache;
}
```

## Logging System

The logging system supports multiple log levels:

- **DEBUG (0)**: Detailed debugging information
- **INFO (1)**: General information about application flow
- **WARN (2)**: Warning messages
- **ERROR (3)**: Error messages

Log messages include:
- Timestamp
- Log level
- Message
- Context information (as JSON)

## Error Handling Strategy

The application uses a consistent error handling strategy:

1. **Try/Catch Blocks**: All operations that might fail are wrapped in try/catch blocks
2. **Error Logging**: Errors are logged with context information
3. **User Feedback**: User-friendly error messages are displayed in the UI
4. **Graceful Degradation**: The application continues to function even if some operations fail

## Performance Considerations

To optimize performance:

1. **Configuration Caching**: Configuration values are cached in memory
2. **Batch Operations**: API calls are batched where possible
3. **Selective Field Retrieval**: Only necessary fields are requested from the API
4. **Pagination**: Large datasets are paginated with a page size of 1000

## Security Considerations

1. **Authentication**: API calls use bearer token authentication
2. **No Sensitive Data in Logs**: Sensitive data is not logged
3. **User Permissions**: The application respects Google Sheets permissions

## Testing

Currently, the application does not have automated tests. Manual testing should focus on:

1. **API Integration**: Verify that API calls work correctly
2. **Data Transformation**: Verify that data is transformed correctly
3. **Error Handling**: Verify that errors are handled gracefully
4. **UI Operations**: Verify that UI operations work as expected

## Known Issues and Limitations

1. **Limited Error Recovery**: Some errors may require manual intervention
2. **No Offline Support**: The application requires an internet connection
3. **Limited Validation**: Some validation is basic and may not catch all errors
4. **No Concurrency Control**: Multiple users editing the same sheet may cause conflicts

## Future Development Roadmap

1. **Add Unit Tests**: Implement unit tests for critical functions
2. **Improve Error Handling**: Add more robust error recovery
3. **Enhance Validation**: Add more comprehensive data validation
4. **Add Caching**: Implement caching for API responses
5. **Improve UI**: Add progress indicators and better user feedback
