# CSV Integration Sheet

A Google Apps Script project for integrating Google Sheets with TOTVS CRM through API calls. This application allows users to manage price lists, products, and customer data between Google Sheets and the CRM system.

## Project Overview

This application serves as a bridge between Google Sheets and TOTVS CRM, enabling users to:

1. Fetch reference data from the CRM API
2. Transform and validate data in Google Sheets
3. Perform upsert operations to send data back to the CRM
4. Generate and download CSV files with transformed data

## Architecture

The project follows a modular architecture with clear separation of concerns:

### Core Modules

- **api.js**: Centralized API client for making requests to the TOTVS CRM API
- **config.js**: Configuration management with caching
- **logger.js**: Structured logging system
- **validation.js**: Data validation utilities
- **main.js**: Application entry point and initialization

### Feature Modules

- **products.js**: Handles product data retrieval and transformation
- **priceLists.js**: Manages price lists and price list items
- **customerPersonTypes.js**: Handles customer person type data
- **prepareUpdate.js**: Prepares data for update operations
- **export.js**: Handles data export and upsert operations
- **utils.js**: UI and utility functions

## Key Functions

### Initialization

- `initialize()`: Main entry point, sets up logging and creates the custom menu
- `onOpen()`: Creates the custom menu in the Google Sheets UI

### Data Retrieval

- `getProducts()`: Fetches product data from the CRM API
- `getPriceLists()`: Fetches price list data from the CRM API
- `getPriceListItems()`: Fetches price list items from the CRM API
- `getCustomerPersonTypes()`: Fetches customer person types from the CRM API

### Data Transformation

- `replaceCode()`: Replaces code values with IDs from the API
- `prepareUpdate()`: Prepares data for update operations
- `addExternalIdColumn()`: Adds an external ID column for integration

### Data Validation

- `validateRequiredColumns()`: Validates that required columns have values
- `validateUUIDColumns()`: Validates that UUID columns contain valid UUIDs
- `validateSheet()`: Validates that a sheet exists and has data

### API Communication

- `apiGet()`: Makes GET requests to the CRM API
- `apiPost()`: Makes POST requests to the CRM API
- `processApiResponse()`: Standardizes API response handling

### Configuration

- `getConfiguration()`: Gets configuration values from the configuration sheet
- `setConfiguration()`: Sets configuration values in the configuration sheet
- `initConfigCache()`: Initializes the configuration cache

### Logging

- `logDebug()`, `logInfo()`, `logWarn()`, `logError()`: Logging functions for different levels
- `logException()`: Logs exceptions with stack traces
- `setLogLevel()`: Sets the current log level

### UI Operations

- `handlePriceListItemsUpsert()`: Handles price list items upsert operation
- `handleCustomersUpsert()`: Handles customers upsert operation
- `downloadReplacedCSV()`: Downloads a CSV file with replaced values
- `openUpsertPage()`, `openDeletePage()`: Opens import pages in the browser

## Required Sheets

The application requires the following sheets to be present in the spreadsheet:

1. **configuration**: Contains configuration key-value pairs
2. **priceListItems**: Contains price list item data
3. **customers**: Contains customer data

## Configuration Keys

The following configuration keys are used:

- `auth_token`: Authentication token for the CRM API
- `api_base_url`: Base URL for the CRM API
- `gateway_send_url`: URL for sending data to the gateway
- `upsert_importation_url`: URL for the upsert importation page
- `delete_importation_url`: URL for the delete importation page
- `importation_identifier`: Identifier for the importation process

## Development Guidelines

### Adding New Features

1. Create a new module file if the feature is substantial
2. Use the existing modules for API calls, configuration, and logging
3. Follow the error handling patterns established in the codebase
4. Update the custom menu in `onOpen()` if adding user-facing features

### Error Handling

- Use try/catch blocks for all operations that might fail
- Log errors with appropriate context
- Provide user-friendly error messages in the UI

### Logging

- Use the appropriate log level for different types of messages
- Include context information in log messages
- Use `console.log()` for simple messages and the logging module for structured logging

### Configuration

- Store all configuration values in the configuration sheet
- Use `getConfiguration()` to retrieve configuration values
- Provide sensible defaults for optional configuration values

## Technical Constraints

As a Google Apps Script project, this application has certain constraints:

1. Limited access to external libraries
2. No module imports/exports (all functions are globally scoped)
3. Limited ES6+ features support
4. Execution time limits for scripts
5. Quotas for API calls and other operations

## Troubleshooting

### Common Issues

1. **Configuration errors**: Ensure all required configuration values are set in the configuration sheet
2. **API errors**: Check the API credentials and endpoints
3. **Sheet structure errors**: Ensure the required sheets and columns exist
4. **Execution timeout**: Break down long-running operations into smaller chunks

### Debugging

1. Use `console.log()` to output debug information
2. Check the Execution Transcript in the Apps Script editor
3. Use the `runHealthCheck()` function to verify the application setup

## Future Improvements

1. Add unit testing framework
2. Implement retry logic for API calls
3. Add more robust input validation
4. Implement caching for frequently accessed data
5. Add progress indicators for long-running operations
